/**
 * 元数据管理 - 自定义算子
 */

import {IOperatorDetailRes, IOperatorPathcParams, IOperVersionOneRes} from '@api/metaRequest';
import {AxiosProgressEvent} from 'axios';
import {BaseResponseType, request, urlPrefix} from '../apiFunction';

type ApiResPromise<T> = BaseResponseType<T>;

// 算子类型语言
export enum OperatorTypeLanguageEnum {
  PYTHON = 'PYTHON'
}

export const OperatorTypeLanguageMap = {
  [OperatorTypeLanguageEnum.PYTHON]: 'Python'
};

// 算子类型引擎
export enum OperatorTypeEngineEnum {
  SPARK = 'SPARK',
  RAY = 'RAY'
}
export const OperatorTypeEngineMap = {
  [OperatorTypeEngineEnum.SPARK]: 'Spark',
  [OperatorTypeEngineEnum.RAY]: 'Ray'
};

// 算子字段类型
export enum OperatorFieldTypeEnum {
  STRING = 'STRING',
  INT = 'INT',
  FLOAT = 'FLOAT',
  BOOLEAN = 'BOOLEAN',
  ARRAY = 'ARRAY',
  DICT = 'DICT',
  ANY = 'ANY'
}
export const OperatorFieldTypeMap = {
  [OperatorFieldTypeEnum.STRING]: 'String',
  [OperatorFieldTypeEnum.INT]: 'Int',
  [OperatorFieldTypeEnum.FLOAT]: 'Float',
  [OperatorFieldTypeEnum.BOOLEAN]: 'Boolean',
  [OperatorFieldTypeEnum.ARRAY]: 'Array',
  [OperatorFieldTypeEnum.DICT]: 'Dict',
  [OperatorFieldTypeEnum.ANY]: 'Any'
};
// 算子资源类型
export enum OperatorResourceTypeEnum {
  CPU = 'CPU',
  GPU = 'GPU'
}
export const OperatorResourceTypeMap = {
  [OperatorResourceTypeEnum.CPU]: 'CPU',
  [OperatorResourceTypeEnum.GPU]: 'GPU'
};

// 继承 IOperVersionOneRes
export interface IOperator extends IOperatorPathcParams {
  catalogName: string;
  schemaName: string;
}
export interface IOperVersion extends IOperVersionOneRes {}

// 创建算子
export function createOperator(
  workspaceId: string,
  operator: IOperatorPathcParams
): ApiResPromise<IOperVersionOneRes> {
  return request({
    url: `${urlPrefix}/operators`,
    method: 'POST',
    data: {...operator},
    params: {workspaceId}
  });
}
export function operatorDetail(
  workspaceId: string,
  {fullName, versionName}: {fullName: string; versionName: string}
): ApiResPromise<IOperatorDetailRes> {
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions/${versionName}`,
    method: 'GET',
    params: {workspaceId}
  }).then((res: any) => {
    // TODO: 临时处理 添加权限
    return {
      ...res,
      result: {
        ...res.result,
        privileges: res.result?.privileges || ['MANAGE', 'CREATE_OPERATOR_VERSION']
      }
    };
  });
}

export interface IBaseImage {
  baseImageId: string;
  imageName: string;
  engine: string;
  resourceType: string;
  mirrorVersion: string;
  databuilderVersion: string;
  imageUrl: string;
  description: string;
}
// 镜像
export function baseImages(engineType?: string, resourceType?: string): ApiResPromise<IBaseImage> {
  return request({
    url: `${urlPrefix}/base_images`,
    method: 'GET',
    params: {engineType, resourceType}
  });
}
// 修改算子
export function editOperator(
  workspaceId: string,
  fullName: string,
  operator: IOperatorPathcParams
): ApiResPromise<IOperVersionOneRes> {
  return request({
    url: `${urlPrefix}/operators/${fullName}`,
    method: 'PATCH',
    data: {...operator},
    params: {workspaceId}
  });
}

// 删除 Operator
export function deleteOperator(workspaceId: string, fullName: string): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/operators/${fullName}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}
// 删除 Operator 版本
export function deleteOperatorVersion(
  workspaceId: string,
  fullName: string,
  version: string
): ApiResPromise<any> {
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions/${version}`,
    method: 'DELETE',
    params: {workspaceId}
  });
}

// 创建算子版本
export function createOperatorVersion(
  workspaceId: string,
  operatorVersion: IOperVersion
): ApiResPromise<IOperVersion> {
  return request({
    url: `${urlPrefix}/operators/versions`,
    method: 'POST',
    data: {...operatorVersion},
    params: {workspaceId}
  });
}

// 修改算子版本
export function editOperatorVersion(
  workspaceId: string,
  operatorVersion: IOperVersion
): ApiResPromise<IOperVersion> {
  const fullName = `${operatorVersion.catalogName}.${operatorVersion.schemaName}.${operatorVersion.operatorName}`;
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions/${operatorVersion.version}`,
    method: 'PATCH',
    data: {...operatorVersion},
    params: {workspaceId}
  });
}

// 上传算子版本文件

export function uploadFileOperatorVersion(params: {
  fullName: string;
  versionName: string;
  workspacePath: string;
  workspaceId: string;
  fileUploadType: string;
  formData?: FormData;
  onProgress?: (event: AxiosProgressEvent) => void;
}): BaseResponseType<any> {
  const {fullName, versionName, workspacePath, workspaceId, fileUploadType, formData, onProgress} = params;
  return request({
    url: `${urlPrefix}/operators/${fullName}/versions/${versionName}/files`,
    method: 'POST',
    params: {
      fileUploadType,
      workspacePath,
      workspaceId
    },
    data: formData,
    onUploadProgress: onProgress
  });
}
