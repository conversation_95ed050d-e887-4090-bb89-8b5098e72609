import React, {forwardRef, useImperativeHandle, useState, useRef} from 'react';
import {Modal, Button, Loading, Input, Select, Link, Dropdown, Menu} from 'acud';
import {getResourceList, getWorkspaceUserList, updateResourcePermission} from '@api/permission';
import {ResourceType, PrivilegeType, Privilege} from '@api/permission/type';
import Arrow from '@assets/originSvg/notebook/arrow.svg';
import UserIcon from '@assets/originSvg/user-line.svg';
import UserGroupIcon from '@assets/originSvg/user-group-line.svg';
import UserAllIcon from '@assets/originSvg/user-all.svg';

import styles from './index.module.less';
import classNames from 'classnames/bind';
import _ from 'lodash';
const cx = classNames.bind(styles);

interface PermissionModalProps {
  workspaceId: string;
  onSuccess?: () => void;
  onClose?: (hasUpdate: boolean) => void;
}

interface PermissionPayload {
  // 资源类型
  resourceType: ResourceType;
  // 资源id
  resourceId: string;
  // 资源名称
  resourceName: string;
  // 资源权限列表
  privilegeList: {value: string; label: string}[];
}

export interface PermissionModalRef {
  open: (payload: PermissionPayload) => void;
  close: () => void;
}

const PermissionModal = forwardRef<PermissionModalRef, PermissionModalProps>((props, ref) => {
  const [visible, setVisible] = useState(false);
  const [payload, setPayload] = useState<PermissionPayload | null>(null);
  const [step, setStep] = useState(1);
  const [userList, setUserList] = useState<any[]>([]);
  const [wsUserList, setWsUserList] = useState<any[]>([]);
  const hasUpdate = useRef(false);

  async function refreshUserList(resourceType: ResourceType, resourceId: string) {
    const res = await getResourceList(resourceType, resourceId, props.workspaceId);
    if (res.success) {
      const list = _.map(res.result, (item) => {
        if (item.principal.type === 'ROLE' && item.principal.name === '空间全部用户') {
          item.principal.id = 'all';
        }
        return item;
      });
      setUserList(list);
    }
  }

  useImperativeHandle(ref, () => ({
    open: async (payload: PermissionPayload) => {
      setVisible(true);
      setPayload(payload);
      setSelectedPrivilege(payload.privilegeList[0].value as Privilege);
      refreshUserList(payload.resourceType, payload.resourceId);
    },
    close: handleClose
  }));

  const handleClose = () => {
    setVisible(false);
    setPayload(null);
    setUserList([]);
    setWsUserList([]);
    setSelectedPrivilege(null);
    setSelectedUserIds([]);
    setStep(1);
    props.onClose?.(hasUpdate.current);
    hasUpdate.current = false;
  };

  const [step1Loading, setStep1Loading] = useState(false);
  const renderUserItem = (item: any) => {
    const renderPrivilegeBlock = () => {
      if (item.privilege.privilege === 'OWNER') {
        return (
          <div>
            <span>管理（所有者）</span>
          </div>
        );
      }
      if (item.inheritedFrom) {
        const privilege = payload.privilegeList.find(
          (privilege) => privilege.value === item.privilege.privilege
        );
        return (
          <div>
            <span>{privilege?.label}（继承）</span>
          </div>
        );
      }

      const handleDelete = async (item: any) => {
        setStep1Loading(true);
        try {
          const res = await updateResourcePermission(
            payload.resourceType,
            payload.resourceId,
            [
              {
                principal: item.principal,
                removePrivileges: [
                  {
                    privilege: item.privilege.privilege,
                    type: PrivilegeType.Single
                  }
                ]
              }
            ],
            props.workspaceId
          );
          if (res.success) {
            refreshUserList(payload.resourceType, payload.resourceId);
            props.onSuccess?.();
            hasUpdate.current = true;
          }
          setStep1Loading(false);
        } catch (error) {
          setStep1Loading(false);
        }
      };

      const onChange = async (value: string) => {
        if (value === 'delete') {
          handleDelete(item);
          return;
        }
        setStep1Loading(true);
        try {
          const res = await updateResourcePermission(
            payload.resourceType,
            payload.resourceId,
            [
              {
                principal: item.principal,
                addPrivileges: [
                  {
                    privilege: value as any,
                    type: PrivilegeType.Single
                  }
                ],
                removePrivileges: [
                  {
                    privilege: item.privilege.privilege,
                    type: PrivilegeType.Single
                  }
                ]
              }
            ],
            props.workspaceId
          );
          if (res.success) {
            refreshUserList(payload.resourceType, payload.resourceId);
            props.onSuccess?.();
            hasUpdate.current = true;
          }
          setStep1Loading(false);
        } catch (error) {
          setStep1Loading(false);
        }
      };

      return (
        <div>
          <SelectPrivilege
            value={item.privilege.privilege}
            options={payload.privilegeList}
            onChange={onChange}
          ></SelectPrivilege>
        </div>
      );
    };

    return (
      <div className={cx('user-item')} key={item.principal.id}>
        <div className={cx('user-info')}>
          <span
            className={cx('principal-type', item.principal.type, {
              OWNER: item.privilege.privilege === 'OWNER'
            })}
          ></span>
          <span className={cx('principal-name')} title={item.principal.name}>
            {item.principal.name}
          </span>
        </div>
        <div>{renderPrivilegeBlock()}</div>
      </div>
    );
  };

  const goStep2 = async () => {
    setStep(2);
    const res = await getWorkspaceUserList({workspaceId: props.workspaceId});
    if (res.success) {
      const list = [
        {
          principal: {
            id: 'all',
            name: '空间全部用户',
            type: 'ROLE'
          }
        },
        ...res.result.principalInfos
      ];
      setWsUserList(list);
    }
  };

  const goStep1 = () => {
    setStep(1);
    setSelectedUserIds([]);
    setSelectedPrivilege(payload.privilegeList[0].value as Privilege);
  };

  const wsUserListOptions = wsUserList.map((item) => ({
    label: item.principal.name,
    value: item.principal.id,
    type: item.principal.type,
    disabled: _.find(userList, (user) => user.principal.id === item.principal.id && !user.inheritedFrom)
  }));

  const [selectedUserIds, setSelectedUserIds] = useState<string[]>([]);
  const [selectedPrivilege, setSelectedPrivilege] = useState<Privilege | null>(null);
  const [addLoading, setAddLoading] = useState(false);
  const onAddUserPrivilege = async () => {
    if (addLoading) return;

    setAddLoading(true);
    const selectedUsers = selectedUserIds.map((id) => {
      return {
        principal: wsUserList.find((item) => item.principal.id === id)?.principal,
        addPrivileges: [
          {
            privilege: selectedPrivilege,
            type: PrivilegeType.Single
          }
        ]
      };
    });
    try {
      const res = await updateResourcePermission(
        payload.resourceType,
        payload.resourceId,
        selectedUsers,
        props.workspaceId
      );
      if (res.success) {
        goStep1();
        refreshUserList(payload.resourceType, payload.resourceId);
        props.onSuccess?.();
        hasUpdate.current = true;
      }
      setAddLoading(false);
    } catch (error) {
      setAddLoading(false);
    }
  };

  const renderUserIcon = (option) => {
    if (option.type === 'USER') {
      return <UserIcon />;
    }
    if (option.type === 'GROUP') {
      return <UserGroupIcon />;
    }
    if (option.type === 'ROLE') {
      return <UserAllIcon />;
    }
  };

  return (
    <Modal
      className={cx('permission-modal')}
      title={`权限：${payload?.resourceName}`}
      visible={visible}
      onCancel={handleClose}
      footer={null}
    >
      <div className={cx('modal-content')}>
        {step1Loading && <Loading loading={true} size="small" />}
        {step === 1 && (
          <div className={cx('step1')}>
            <Input
              className={cx('mb-[20px]')}
              placeholder="输入以添加多个用户、组或空间内全部用户"
              onClick={goStep2}
            />
            <div className={cx('user-list')}>{userList.map((item) => renderUserItem(item))}</div>
          </div>
        )}
        {step === 2 && (
          <div className={cx('step2')}>
            <div className={cx('select-wrap')}>
              <Select
                value={selectedUserIds}
                mode="multiple"
                maxTagCount={'responsive'}
                className={cx('user-select')}
                dropdownClassName={cx('user-select-dropdown')}
                placeholder="输入以添加多个用户、组或空间内全部用户"
                options={wsUserListOptions}
                showSelectAll={false}
                optionFilterProp="label"
                onChange={(value) => setSelectedUserIds(value)}
                optionRender={(label, option) => {
                  return (
                    <div className={cx('user-option')}>
                      <span className={cx('user-name')} title={option.label}>
                        <span className={cx('user-icon')}>{renderUserIcon(option)}</span>
                        <span className={cx('user-name-text')}>{option.label}</span>
                      </span>
                    </div>
                  );
                }}
              ></Select>
              <Select
                value={selectedPrivilege}
                className={cx('privilege-select')}
                placeholder="请选择角色"
                options={payload.privilegeList}
                onChange={(value) => setSelectedPrivilege(value)}
              ></Select>
            </div>
            <div className={cx('btn-wrap')}>
              <Button onClick={goStep1}>取消</Button>
              <Button
                type="primary"
                disabled={!selectedUserIds.length || !selectedPrivilege}
                onClick={onAddUserPrivilege}
                loading={addLoading}
              >
                确定
              </Button>
            </div>
          </div>
        )}
      </div>
    </Modal>
  );
});

PermissionModal.displayName = 'PermissionModal';

export default PermissionModal;

// 权限选择组件
interface SelectProps {
  value: string;
  options: {value: string; label: string}[];
  onChange: (value: string) => void;
  disabled?: boolean;
}

function SelectPrivilege(props: SelectProps) {
  const activeLabel = props.options.find((item) => item.value === props.value)?.label || props.value;
  return (
    <div className={cx('select-privilege')}>
      <Dropdown
        trigger={['click']}
        placement="bottomRight"
        overlayClassName={cx('privilege-dropdown-menu')}
        overlay={
          <Menu
            className={cx('dropdown-menu')}
            selectedKeys={[props.value]}
            onClick={(e) => props.onChange(e.key)}
            disabled={props?.disabled}
          >
            {props.options.map((item) => (
              <Menu.Item key={item.value}>{item.label}</Menu.Item>
            ))}
            <div className="h-[1px] bg-[#E5E7EB] mx-[8px] my-[8px]"></div>
            <Menu.Item key="delete">删除</Menu.Item>
          </Menu>
        }
      >
        <div className={cx('select-privilege-text')}>
          {activeLabel}
          <Arrow className={cx('arrow')} />
        </div>
      </Dropdown>
    </div>
  );
}
