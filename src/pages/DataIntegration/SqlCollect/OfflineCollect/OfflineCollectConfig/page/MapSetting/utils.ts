import {
  BatchObj,
  BatchTableColumns,
  getDatasourceTableColumns,
  Partitioning,
  SourceMapping
} from '@api/integration/batch';
import {getTableDetail, ITableDetailRes} from '@api/metaRequest';
import {
  DbTypeMap,
  JsPlumbIdEnum,
  SinkNameRuleEnum,
  SinkTypeEnum,
  SourceConfigEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {RULE} from '@utils/regs';

// 检查目标表字段名称是否重复
export const checkTargetTableName = (tableData: BatchTableColumns[], index?: number) => {
  const record = tableData[index];
  const text = tableData[index].name;
  if (!text) {
    return '请输入字段名称';
  }
  if (tableData.some((item) => item.name === text && item.id !== record.id)) {
    return '字段名称不能重复';
  }
  if (!RULE.specialName50.test(text)) {
    return RULE.specialNameStartEn64Text;
  }
  return '';
};
// 检查分区表字段名称是否重复
export const checkPartitioningTableName = (tableData: Partitioning[], index?: number) => {
  const record = tableData[index];
  const text = tableData[index].name;
  let warningText = '';
  if (tableData.some((item, i) => item.name === text && i !== index && item.function === record.function)) {
    warningText = '字段名称+转换函数 不能重复';
  }
  return warningText;
};
// 检查目标表字段
export const checkTargetTable = (tableData: BatchTableColumns[]): boolean => {
  for (let i = 0; i < tableData.length; i++) {
    if (checkTargetTableName(tableData, i)) {
      return false;
    }
  }
  return true;
};

// 检查分区表字段名称
export const checkPartitioningTable = (tableData: BatchTableColumns[]): boolean => {
  for (let i = 0; i < tableData.length; i++) {
    if (checkPartitioningTableName(tableData, i)) {
      return false;
    }
  }
  return true;
};

// 检查最少有一个连线
export const checkMapping = (tableData: SourceMapping[]): boolean => {
  if (tableData?.length > 0) {
    return true;
  }
  return false;
};

const dealFullTableNameToName = (name: string) => {
  return name.split('.')?.[2];
};
// 处理目标表名称
export const dealTargetTable = (batchObj: BatchObj) => {
  // 如果是自动创建 表并没有创建
  if (batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated && !batchObj.sinkFullName) {
    let tableName = batchObj?.[SourceConfigEnum.SourceConfig]?.sourceTable;
    const sinkNameRule = batchObj?.[SourceConfigEnum.SinkConfig]?.sinkNameRule;
    const prefix = batchObj?.[SourceConfigEnum.SinkConfig]?.prefix;
    const suffix = batchObj?.[SourceConfigEnum.SinkConfig]?.suffix;
    if (sinkNameRule === SinkNameRuleEnum.ADDPREFIX || sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX) {
      tableName = prefix + tableName;
    }
    if (sinkNameRule === SinkNameRuleEnum.ADDSUFFIX || sinkNameRule === SinkNameRuleEnum.ADDPREFIXANDSUFFIX) {
      tableName = tableName + suffix;
    }
    return tableName;
  } else if (batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated && batchObj.sinkFullName) {
    // 如果是自动创建 表已经创建
    return dealFullTableNameToName(batchObj.sinkFullName);
  } else {
    // 如果是手动创建
    return dealFullTableNameToName(batchObj?.[SourceConfigEnum.SinkConfig]?.sinkPath);
  }
};

/** 转换不合法的字段类型 */
export const transInvalidType = (data) => {
  const {type, precision, scale} = data;
  if (type === 'VARCHAR') {
    return precision > 0 && precision <= 65522 ? data : {...data, precision: 65522};
  }
  if (type === 'CHAR') {
    return precision > 0 && precision <= 255 ? data : {...data, precision: 255};
  }
  if (type === 'DECIMAL') {
    return precision > 0 && precision <= 38 && precision > scale && scale > 0
      ? data
      : {...data, precision: 38, scale: 18};
  }
  if (type === 'DATETIME') {
    return precision > 0 && precision <= 6 ? data : {...data, precision: 6};
  }
  return data;
};

/** 处理目标表字段id */
// 目标表字段 都是必填
export const targetColumnsDealId = (columns: any[]) => {
  return columns.map((item) => {
    const data = {
      id: JsPlumbIdEnum.TARGET + String(item.name),
      defaultValue: '',
      nullable: false,
      description: '',
      precision: 0,
      scale: 0,
      isPrimaryKey: false,
      function: '',
      functionParameter: 0,
      comment: '',
      ...item
    };
    return transInvalidType(data);
  });
};

/** 字段类型映射 */
export const typeMapFn = (type: string, sinkType: SinkTypeEnum) => {
  return DbTypeMap[sinkType][type] || 'STRING';
};

// 处理元数据 到 目标端字段
export const dealMetaDataTable = (obj: ITableDetailRes): BatchTableColumns[] => {
  return targetColumnsDealId(
    obj.columns?.map((item) => ({
      type: item.typeName,
      dbType: item.typeName,
      precision: item.typePrecision ?? 0,
      scale: item.typeScale ?? 0,
      ...item
    }))
  );
};

// 自动创建 处理 mapping 数据
const autoCreateMapping = async (batchObj: BatchObj): Promise<BatchObj> => {
  const columns = batchObj.sourceTable;
  batchObj[SourceConfigEnum.MappingConfig].sinkFields = columns.map((item) => {
    return {
      ...item,
      comment: item.description || '',
      isPrimaryKey: item.primaryKey,
      type: typeMapFn(item.dbType, batchObj?.[SourceConfigEnum.SinkConfig]?.sinkType)
    };
  });
  batchObj[SourceConfigEnum.MappingConfig].mapping = columns.map((item) => ({
    sourceColumn: item.name,
    sinkColumn: item.name
  }));
  return batchObj;
};

// 源端变化 处理 mapping 数据
export const initMapping = async (workspaceId: string, batchObj: BatchObj) => {
  const mapping = batchObj?.[SourceConfigEnum.MappingConfig]?.mapping;
  const sinkFields = batchObj?.[SourceConfigEnum.MappingConfig]?.sinkFields;
  const partitioning = batchObj?.[SourceConfigEnum.MappingConfig]?.sinkPartitions;

  if (!batchObj.sourceTable) {
    const isSQLServerOrPostgreSQL = ['SQLServer', 'PostgreSQL', 'HANA'].includes(
      batchObj[SourceConfigEnum.SourceConfig].sourceType
    );
    const {result} = await getDatasourceTableColumns({
      environment: {workspaceId: batchObj.workspaceId, computeId: batchObj.compute.computeId},
      datasourceInfo: {
        connectionId: batchObj[SourceConfigEnum.SourceConfig].sourceConnectionId,
        database: batchObj[SourceConfigEnum.SourceConfig].sourceDatabase,
        table: batchObj[SourceConfigEnum.SourceConfig].sourceTable,
        ...(isSQLServerOrPostgreSQL ? {schema: batchObj[SourceConfigEnum.SourceConfig].sourceSchema} : {})
      }
    });
    batchObj.sourceTable = result.columns;
  }

  let resultBatchObj: BatchObj = batchObj;
  // 说明没有处理源端变化
  const flag = mapping?.length === 0 && sinkFields?.length === 0 && partitioning?.length === 0;
  if (flag) {
    const isAutoCreated = batchObj?.[SourceConfigEnum.SinkConfig]?.isAutoCreated;
    if (isAutoCreated) {
      resultBatchObj = await autoCreateMapping(batchObj);
    } else {
      const res = await getTableDetail(workspaceId, batchObj?.[SourceConfigEnum.SinkConfig]?.sinkPath);
      batchObj[SourceConfigEnum.MappingConfig].sinkFields = dealMetaDataTable(res.result);
    }
  }
  return resultBatchObj;
};
