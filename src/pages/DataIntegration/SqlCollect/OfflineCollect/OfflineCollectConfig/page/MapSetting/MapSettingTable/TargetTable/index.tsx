import {BatchTableColumns, SourceMapping} from '@api/integration/batch';
import {
  DbTypeMap,
  DbTypeObject,
  JsPlumbIdEnum,
  SinkTypeEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {RULE} from '@utils/regs';
import {Button, Checkbox, Input, InputNumber, Select, Space, Table, Tag} from 'acud';
import {ColumnType} from 'acud/lib/table';
import {useMemoizedFn} from 'ahooks';
import React, {useCallback, useMemo, useRef} from 'react';
import styles from '../index.module.less';
import {checkTargetTableName} from '../../utils';
import IconSvg from '@components/IconSvg';
import {Ellipsis} from '@baidu/bce-react-toolkit';

const TargetTable: React.FC<{
  tableData: BatchTableColumns[];
  onChangeList?: (data: BatchTableColumns[]) => void;
  deleteNode?: (id: string) => void;
  isReadOnly?: boolean;
  isPrimaryKeyReadOnly?: boolean;
  showDelete?: boolean;
  onWarning?: (flag: boolean) => void;
  sinkType?: SinkTypeEnum;
  onChangeMapping: (mapping) => void;
  mapping?: SourceMapping[];
}> = ({
  tableData,
  onChangeList,
  deleteNode,
  isReadOnly = true,
  isPrimaryKeyReadOnly = true,
  showDelete = false,
  onWarning,
  onChangeMapping,
  mapping,
  sinkType
}) => {
  /** 修改数据  id: 索引   dataIndex: 修改的dataIndex value: 修改后的值  */
  const handleChange = useMemoizedFn((id: string, dataIndex: string, value: string | boolean | number) => {
    const newData = tableData.map((item) => {
      if (item.id === id) {
        return {...item, [dataIndex]: value};
      }
      return item;
    });
    onChangeList(newData);
  });

  /** 添加数据 */
  const handleAdd = useMemoizedFn(() => {
    const newRow: BatchTableColumns = {
      id: JsPlumbIdEnum.TARGET + String(new Date().getTime()),
      name: '',
      type: 'STRING',
      defaultValue: '',
      dbType: 'STRING',
      nullable: false,
      description: '',
      precision: 0,
      scale: 0,
      isPrimaryKey: false,
      function: '',
      functionParameter: 0,
      comment: ''
    };
    onChangeList([...tableData, newRow]);
  });

  /** 删除数据 */
  const handleDelete = useMemoizedFn((id: string) => {
    deleteNode?.(id);
    onChangeList(tableData.filter((item) => item.id !== id));
    onChangeMapping(mapping.filter((item) => item.sinkColumn !== id));
  });

  // 是否禁用
  const handleDisabled = useMemoizedFn((id: string) => {
    deleteNode?.(id);
    onChangeList(tableData.filter((item) => item.id !== id));
  });

  const renderPrecision = useCallback(
    (isReadOnly, type, id, precision, scale?) => {
      const config = {
        DECIMAL: {
          readOnly: (
            <div>
              （{precision},{scale}）
            </div>
          ),
          editable: (
            <Space>
              <InputNumber
                style={{width: 60}}
                size="small"
                value={precision}
                min={1}
                max={38}
                onChange={(e) => handleChange(id, 'precision', e)}
              />
              <InputNumber
                style={{width: 60}}
                size="small"
                value={scale}
                min={1}
                max={38}
                onChange={(e) => handleChange(id, 'scale', e)}
              />
            </Space>
          )
        },
        CHAR: {
          readOnly: <div>（{precision}）</div>,
          editable: (
            <Space>
              <InputNumber
                style={{width: 60}}
                size="small"
                value={precision}
                min={1}
                max={255}
                onChange={(e) => handleChange(id, 'precision', e)}
              />
            </Space>
          )
        },
        VARCHAR: {
          readOnly: <div>（{precision}）</div>,
          editable: (
            <Space>
              <InputNumber
                style={{width: 60}}
                size="small"
                value={precision}
                min={1}
                max={65522}
                onChange={(e) => handleChange(id, 'precision', e)}
              />
            </Space>
          )
        },
        DATETIME: {
          readOnly: <div>（{precision}）</div>,
          editable: (
            <Space>
              <InputNumber
                style={{width: 60}}
                size="small"
                value={precision}
                min={1}
                max={6}
                onChange={(e) => handleChange(id, 'precision', e)}
              />
            </Space>
          )
        }
      };
      return config?.[type]?.[isReadOnly ? 'readOnly' : 'editable'];
    },
    [handleChange]
  );

  /** 表格列配置 */
  const columns: ColumnType<BatchTableColumns>[] = [
    {
      title: '序号',
      width: 50,
      dataIndex: 'index',
      render: (text: string, record: BatchTableColumns, index: number) => <div>{index + 1}</div>,
      fixed: 'left'
    },
    {
      title: '字段名称',
      dataIndex: 'name',
      width: 120,
      key: 'name',
      fixed: 'left',
      render: (text: string, record: BatchTableColumns, index: number) => {
        if (isReadOnly) {
          return text;
        }
        const warningText = checkTargetTableName(tableData, index);
        return (
          <Input
            size="small"
            value={text}
            onChange={(e) => handleChange(record.id, 'name', e.target.value)}
            warningText={warningText}
            warningPopover={!!warningText}
          />
        );
      }
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      width: 170,
      key: 'type',
      render: (text: string, record: BatchTableColumns) =>
        isReadOnly ? (
          <div>{text}</div>
        ) : (
          <Select
            value={text}
            size="small"
            onSelect={(e) => handleChange(record.id, 'type', e)}
            options={Object.keys(DbTypeObject[sinkType]).map((item) => ({
              label: item,
              value: item
            }))}
          />
        )
    },
    {
      title: '精度',
      dataIndex: 'precision',
      width: 150,
      key: 'precision',
      render: (text: string, record: BatchTableColumns) => {
        return renderPrecision(isReadOnly, record.type, record.id, text, record.scale);
      }
    },
    {
      title: '主键',
      dataIndex: 'isPrimaryKey',
      key: 'isPrimaryKey',
      width: 70,
      render: (text: boolean, record: BatchTableColumns) =>
        isPrimaryKeyReadOnly ? (
          text ? (
            <Tag>
              <IconSvg type="sql-partition" size={16} fill="none" />
            </Tag>
          ) : (
            '-'
          )
        ) : (
          <Checkbox
            checked={text}
            onChange={(e) => handleChange(record.id, 'isPrimaryKey', e.target.checked)}
          />
        )
    },
    {
      title: '默认值',
      dataIndex: 'defaultValue',
      key: 'defaultValue',
      width: 100,
      render: (text: string, record: BatchTableColumns) =>
        isReadOnly ? (
          <div>{text}</div>
        ) : (
          <Input
            size="small"
            value={text}
            onChange={(e) => handleChange(record.id, 'defaultValue', e.target.value)}
          />
        )
    },
    {
      title: '字段描述',
      dataIndex: 'comment',
      key: 'comment',
      width: 100,
      render: (text: string, record: BatchTableColumns) =>
        isReadOnly ? (
          <Ellipsis tooltip={text}>{text}</Ellipsis>
        ) : (
          <Input
            size="small"
            value={text}
            onChange={(e) => handleChange(record.id, 'comment', e.target.value)}
          />
        )
    },
    {
      title: '操作',
      key: 'action',
      fixed: 'right',
      width: 80,
      render: (_: any, record: BatchTableColumns) => (
        <Button disabled={tableData.length === 1} type="actiontext" onClick={() => handleDelete(record.id)}>
          移除
        </Button>
      )
    }
  ];

  return (
    <div className={styles['target-table']}>
      <Table
        dataSource={tableData}
        columns={columns.filter((item) => !(item.key === 'action' && !showDelete))}
        pagination={false}
        rowKey="id"
        scroll={{x: 800}}
        rowClassName={(record, index) => {
          return record.id + ' ' + JsPlumbIdEnum.TARGET;
        }}
      />
      {!isReadOnly && (
        <Button className={styles['add-button']} onClick={handleAdd} type="default">
          添加字段
        </Button>
      )}
    </div>
  );
};

export default TargetTable;
