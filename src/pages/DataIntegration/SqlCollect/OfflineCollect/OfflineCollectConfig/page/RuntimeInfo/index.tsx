import FilePathSelect, {PathLevel} from '@components/FilePathSelect';
import {
  DirtyDataStrategyChineseMap,
  DirtyDataStrategyEnum,
  SinkModeChineseMap,
  SinkModeEnum,
  SourceChangeHandleEnum,
  SourceChangeHandleChineseMap,
  SourceADDHandleChineseMap,
  SourceConfigEnum,
  SinkTypeEnum
} from '@pages/DataIntegration/SqlCollect/constants';
import {Form, InputNumber, Radio, Select} from 'acud';
import {FormInstance} from 'acud/lib/form';
import React, {useContext, useEffect, useMemo} from 'react';
import CardTwoContent from '../../components/CardTwoContent';
import MaxDirtyItem from '../../components/MaxDirtyItem';
import styles from '../SourceSink/index.module.less';
import FilePathSelectFormItem from '@components/FilePathSelectFormItem';
import {WorkspaceContext} from '@pages/index';
import {searchTableSchema} from '@api/metaRequest';
import {set} from 'lodash';
import {useAsyncEffect} from 'ahooks';

const {Option} = Select;

const labelWidth = 90;
const RuntimeInfo: React.FC<{form: FormInstance; disabled?: boolean}> = ({form, disabled = true}) => {
  const extraMap = {
    ['onAddColumn' + SourceChangeHandleEnum.PAUSE]:
      '当源端表新增字段时，将暂停任务执行，并将任务状态置为失败',
    ['onAddColumn' + SourceChangeHandleEnum.ADD]:
      '当源端表新增字段时，任务继续执行。源端新增字段的数据将丢失不会写入目的地端，该情况也不会产生脏数据。',

    ['onDeleteColumn' + SourceChangeHandleEnum.PAUSE]:
      '当源端表字段删除时，将暂停任务执行，并将任务状态置为失败',
    ['onDeleteColumn' + SourceChangeHandleEnum.SKIP]:
      '当源端表字段删除时，任务继续执行。若映射的目的地字段非空，则会出现写入目的地失败，产生大量脏数据。',

    ['onDeleteSource' + SourceChangeHandleEnum.PAUSE]:
      '当源端表被删除时，将暂停任务执行，并将任务状态置为失败',
    ['dirtyDataStrategy' + DirtyDataStrategyEnum.IGNORE]: '表示忽略脏数据的存在，任务继续运行。',
    ['dirtyDataStrategy' + DirtyDataStrategyEnum.TOLERANT]:
      '表示可接受有一部分脏数据，但是超过阈值之后任务就终止，并且任务状态置为失败。',
    ['dirtyDataStrategy' + DirtyDataStrategyEnum.STRICT]:
      '表示脏数据发生时，任务继续执行，但任务状态置为失败。'
  };

  const onAddColumn = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceChange', 'onAddColumn'], {form});
  const onDeleteColumn = Form.useWatch([SourceConfigEnum.SourceConfig, 'sourceChange', 'onDeleteColumn'], {
    form
  });

  const strategy = Form.useWatch([SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'strategy'], {
    form
  });
  const enableDirtyDataWrite = Form.useWatch(
    [SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'enableDirtyDataWrite'],
    {
      form
    }
  );
  // 源端表字段
  const [tableSchemaList, setTableSchemaList] = React.useState([]);

  const sourceConfig = Form.useWatch(SourceConfigEnum.SourceConfig, form);
  const compute = Form.useWatch('compute', form);
  const parallelism = Form.useWatch([SourceConfigEnum.SourceConfig, 'parallelism'], form);
  const sinkType = Form.useWatch([SourceConfigEnum.SinkConfig, 'sinkType'], form);

  const {workspaceId} = useContext(WorkspaceContext);

  const splitFieldOptions = useMemo(() => {
    return tableSchemaList
      ?.filter((item) =>
        [
          'TINYINT',
          'SMALLINT',
          'INT',
          'BIGINT',
          'DOUBLE',
          'FLOAT',
          'DECIMAL',
          'CHAR',
          'VARCHAR',
          'DATE'
        ].includes(item.dbType)
      )
      .map((item) => ({
        label: item.name,
        value: item.name
      }));
  }, [tableSchemaList]);

  useAsyncEffect(async () => {
    if (parallelism > 1) {
      const {sourceConnectionId, sourceDatabase, sourceTable} = sourceConfig;
      const {computeId} = compute;
      const params = {
        environment: {
          computeId,
          workspaceId
        },
        datasourceInfo: {
          connectionId: sourceConnectionId,
          database: sourceDatabase,
          table: sourceTable
        }
      };
      const res = await searchTableSchema(params);
      setTableSchemaList(res.result.columns);
      const {uniqueKeys, primaryKey} = res.result;
      const defaultSplitField = [
        ...primaryKey,
        ...uniqueKeys.reduce((pre, cur) => [...pre, ...cur.fields], [])
      ].filter((key) => {
        return splitFieldOptions.find((item) => item.value === key);
      })?.[0];
      form.setFieldValue([SourceConfigEnum.SourceConfig, 'splitField'], defaultSplitField);
    }
  }, [parallelism]);

  const left = () => {
    return (
      <>
        <div className={styles['content-left']}>
          <Form.Item
            labelWidth={labelWidth}
            label={<span>开启限速</span>}
            name={[SourceConfigEnum.SourceConfig, 'enableRateLimit']}
          >
            <Radio.Group disabled={true}>
              <Radio value={true}>是</Radio>
              <Radio value={false}>否</Radio>
            </Radio.Group>
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, currentValues) =>
              prevValues[SourceConfigEnum.SourceConfig]?.enableRateLimit !==
              currentValues[SourceConfigEnum.SourceConfig]?.enableRateLimit
            }
            noStyle
          >
            {({getFieldValue}) => {
              const enableRateLimit = getFieldValue([SourceConfigEnum.SourceConfig, 'enableRateLimit']);
              return enableRateLimit ? (
                <>
                  <Form.Item
                    labelWidth={labelWidth}
                    label={'最大流量速率'}
                    name={[SourceConfigEnum.SourceConfig, 'rateLimit', 'flow']}
                  >
                    <InputNumber disabled={disabled} min={1} placeholder="请输入最大并发数" />
                  </Form.Item>

                  <Form.Item
                    labelWidth={labelWidth}
                    label={'最大行数速率'}
                    name={[SourceConfigEnum.SourceConfig, 'rateLimit', 'records']}
                  >
                    <InputNumber disabled={disabled} min={1} placeholder="请输入最大并发数" />
                  </Form.Item>
                </>
              ) : null;
            }}
          </Form.Item>
          <Form.Item
            labelWidth={labelWidth}
            label={'最大并发数'}
            name={[SourceConfigEnum.SourceConfig, 'parallelism']}
          >
            <InputNumber disabled={disabled} min={1} max={100} placeholder="请输入最大并发数" />
          </Form.Item>

          <Form.Item
            shouldUpdate={(prevValues, currentValues) =>
              prevValues[SourceConfigEnum.SourceConfig]?.parallelism !==
              currentValues[SourceConfigEnum.SourceConfig]?.parallelism
            }
            noStyle
          >
            {({getFieldValue}) => {
              const enableRateLimit = getFieldValue([SourceConfigEnum.SourceConfig, 'parallelism']);

              return enableRateLimit > 1 ? (
                <Form.Item
                  labelWidth={labelWidth}
                  label={'分片字段'}
                  name={[SourceConfigEnum.SourceConfig, 'splitField']}
                  rules={[{required: true, message: '请选择分片字段'}]}
                  extra="读取数据时使用该字段进行数据切分，以达到多并发读取数据的效果"
                  preserve={false}
                >
                  <Select placeholder="请选择分片字段" options={splitFieldOptions} />
                </Form.Item>
              ) : null;
            }}
          </Form.Item>

          <Form.Item
            labelWidth={labelWidth}
            label={'源端表删除字段'}
            name={[SourceConfigEnum.SourceConfig, 'sourceChange', 'onDeleteColumn']}
            extra={extraMap['onDeleteColumn' + onDeleteColumn]}
          >
            <Radio.Group disabled={disabled}>
              {Object.entries(SourceChangeHandleChineseMap).map(([value, label]) => (
                <Radio key={value} value={value}>
                  {label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            labelWidth={labelWidth}
            label={'源端表新增字段'}
            name={[SourceConfigEnum.SourceConfig, 'sourceChange', 'onAddColumn']}
            extra={extraMap['onAddColumn' + onAddColumn]}
          >
            <Radio.Group disabled={disabled}>
              {Object.entries(SourceADDHandleChineseMap).map(([value, label]) => (
                <Radio key={value} value={value}>
                  {label}
                </Radio>
              ))}
            </Radio.Group>
          </Form.Item>
          <Form.Item
            labelWidth={labelWidth}
            label={'源端表被删除'}
            name={[SourceConfigEnum.SourceConfig, 'sourceChange', 'onDeleteSource']}
          >
            <Radio.Group disabled={disabled}>
              <Radio value={SourceChangeHandleEnum.PAUSE}>
                {SourceChangeHandleChineseMap[SourceChangeHandleEnum.PAUSE]}
              </Radio>
            </Radio.Group>
          </Form.Item>
          <div>
            注：源端表字段重命名，系统将识别为删除旧字段且新增重命名后的字段，处理遵循删除与新增字段策略
          </div>
        </div>
      </>
    );
  };

  const right = () => {
    return (
      <>
        {sinkType === SinkTypeEnum.iceberg ? (
          <Form.Item
            labelWidth={labelWidth}
            label="写入模式"
            name={[SourceConfigEnum.SinkConfig, 'sinkMode']}
          >
            <Select placeholder="请选择写入模式">
              {Object.values(SinkModeEnum).map((item) => (
                <Option key={item} value={item}>
                  {SinkModeChineseMap[item]}
                </Option>
              ))}
            </Select>
          </Form.Item>
        ) : null}
        <Form.Item
          labelWidth={labelWidth}
          label="脏数据策略"
          name={[SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'strategy']}
          extra={extraMap['dirtyDataStrategy' + strategy]}
          tooltip="脏数据是指写入目的端失败的数据，比如字符串类型数据写入到数值类型中。"
        >
          <Radio.Group disabled={true}>
            {Object.values(DirtyDataStrategyEnum).map((item) => (
              <Radio key={item} value={item}>
                {DirtyDataStrategyChineseMap[item]}
              </Radio>
            ))}
          </Radio.Group>
        </Form.Item>
        {/* 脏数据容忍度 */}
        <MaxDirtyItem form={form} />

        {strategy !== DirtyDataStrategyEnum.TOLERANT && (
          <>
            <Form.Item
              labelWidth={labelWidth}
              label="是否写入脏数据"
              name={[SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'enableDirtyDataWrite']}
              extra="脏数据支持写入到文件，以便于后续处理之后重新写入目的端。选择否时脏数据将直接丢弃"
            >
              <Radio.Group disabled={true}>
                <Radio value={true}>是</Radio>
                <Radio value={false}>否</Radio>
              </Radio.Group>
            </Form.Item>
            {enableDirtyDataWrite ? (
              <Form.Item
                labelWidth={labelWidth}
                label="脏数据存储路径"
                extra="支持脏数据写入到文件，文件名称为任务名称+时间戳，格式为csv"
                name={[SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'dirtyDataVolume']}
              >
                <FilePathSelectFormItem
                  form={form}
                  name={[SourceConfigEnum.SinkConfig, 'dirtyDataStrategy', 'dirtyDataVolume']}
                  disabled={disabled}
                  showPathSelect
                  metaDirs={['volume']}
                  selectableLevel={PathLevel.Volume}
                />
              </Form.Item>
            ) : null}
          </>
        )}
      </>
    );
  };

  return (
    <>
      <div className={'form-item-title'}>读取与写入配置</div>

      <CardTwoContent
        left={left()}
        right={right()}
        title={['源端读取配置', '目标端写入配置']}
      ></CardTwoContent>
    </>
  );
};

export default RuntimeInfo;
