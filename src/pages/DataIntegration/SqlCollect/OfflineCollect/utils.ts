import * as http from '@api/integration';
import {BatchOperateType, IJobItem, Job, JobConfigStatus, JobDetail, JobStatus} from '@api/integration/type';
import {onCopy} from '@pages/DataIntegration/FileCollect/utils';
import urls from '@utils/urls';
import {Modal, toast} from 'acud';
import {Operation, OperationShowType} from '../constants';
import OnOpenModal, {ModalTypeEnum} from './components/OnOpenModal';
import {isArray} from 'lodash';
import {Privilege} from '@api/permission/type';

// callback 参数有差异，暂时写 any
export type OperationConfig = Record<
  OperationShowType,
  {label: string; callback: any; key: Operation; disabled?: boolean; isAuth?: boolean}[] | null
>;

// 删除
export const onDelete = (jobs: IJobItem[], workspaceId: string, callback, force?: boolean) => {
  if (!jobs?.length) {
    return;
  }
  const jobNames = jobs.map((job) => `"${job.name}"`).join('、');
  const isBatch = jobs.length > 1;
  const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(
    jobs[0].status as JobConfigStatus
  );
  Modal.confirm({
    title: `${isBatch ? '批量删除' : '删除任务'}`,
    content: `任务删除后，相关数据将无法恢复，请谨慎操作。确定是否删除${jobNames}？`,
    onOk: async () => {
      try {
        const res = isBatch
          ? await http.batchOperationIntegrationJob(workspaceId, BatchOperateType.Delete, {
              jobIds: jobs.map((item) => item.jobId)
            })
          : await http.deleteIntegrationJob(workspaceId, jobs[0].jobId, {isPublished});
        if (res.success) {
          toast.success({message: `${jobNames}删除成功`, duration: 5});
          callback();
        }
      } catch {
        console.error('删除任务失败');
      }
    }
  });
};

// 前置检查
export const onPreCheck = (jobs: IJobItem[], workspaceId: string, callback, navigate) => {
  OnOpenModal(ModalTypeEnum.PreCheck, jobs, workspaceId, callback, navigate);
};

// 运行
export const onStart = (jobs: IJobItem[], workspaceId: string, callback, navigate) => {
  OnOpenModal(ModalTypeEnum.StartJob, jobs, workspaceId, callback, navigate);
};

// 发布
export const onPublish = (jobs: IJobItem[], workspaceId: string, callback) => {
  if (!jobs?.length) {
    return;
  }
  Modal.confirm({
    title: '发布任务',
    content: '发布任务之后，可以在工作流中被调度执行。',
    onOk: async () => {
      try {
        const res = await http.publishIntegrationJob(workspaceId, jobs[0].jobId);
        if (res.success) {
          toast.success({message: '发布成功', duration: 5});
          callback();
        }
      } catch {
        console.error('发布任务失败');
      }
    }
  });
};

// 编辑
export const onEdit = (id: string, navigate, isPublished: boolean) => {
  navigate(`${urls.offlineCollectConfig}?jobId=${id}&isPublished=${isPublished}`);
};

// 创建
export const onCreate = (navigate) => {
  OnOpenModal(ModalTypeEnum.Create, [], '', () => {}, navigate);
};

// 根据操作配置列表聚合展示操作项
export const getOperationList = (
  navigate,
  obj?: {privileges: string[]; status: JobConfigStatus}
): OperationConfig => {
  const {status, privileges} = obj || {};
  const isPublished = [JobConfigStatus.Published, JobConfigStatus.Updating].includes(status);
  const PreCheck = {
    label: '前置检查',
    key: Operation.PreCheck,
    callback: (jobs: IJobItem[], workspaceId: string, callback) =>
      OnOpenModal(ModalTypeEnum.PreCheck, jobs, workspaceId, callback, navigate),
    isAuth: authCheck(privileges, Privilege.Execute),
    disabled: [JobConfigStatus.Updating, JobConfigStatus.PreCheck, JobConfigStatus.Published].includes(status)
  };
  const Start = {
    label: '运行',
    key: Operation.Start,
    disabled: ![JobConfigStatus.CheckPass, JobConfigStatus.Updating, JobConfigStatus.Published].includes(
      status
    ),
    callback: (jobs: IJobItem[], workspaceId: string, callback) =>
      OnOpenModal(ModalTypeEnum.StartJob, jobs, workspaceId, callback, navigate),
    isAuth: authCheck(privileges, Privilege.Execute)
  };
  const Publish = {
    label: '发布',
    key: Operation.Publish,
    callback: onPublish,
    isAuth: authCheck(privileges, Privilege.Manage),
    disabled: ![JobConfigStatus.CheckPass].includes(status)
  };
  const Edit = {
    label: '编辑',
    key: Operation.Edit,
    isAuth: authCheck(privileges, Privilege.Manage),
    disabled: [JobConfigStatus.Updating, JobConfigStatus.PreCheck].includes(status),
    callback: (jobs: IJobItem[]) => onEdit(jobs?.[0]?.jobId, navigate, isPublished)
  };
  const Copy = {
    label: '复制',
    key: Operation.Copy,
    callback: onCopy,
    disabled: [JobConfigStatus.Draft].includes(status),
    isAuth: authCheck(privileges, Privilege.Manage)
  };
  const Delete = {
    label: '删除',
    key: Operation.Delete,
    callback: onDelete,
    disabled: [JobConfigStatus.Updating, JobConfigStatus.Published].includes(status),
    isAuth: authCheck(privileges, Privilege.Manage)
  };
  const initialList = {
    [OperationShowType.List]: [PreCheck, Start, Publish, Edit],
    [OperationShowType.Dropdown]: [Delete, Copy],
    [OperationShowType.DetailDropdown]: [PreCheck, Edit, Copy, Delete],
    [OperationShowType.DetailList]: [Start],
    [OperationShowType.ResultDropdown]: [PreCheck, Edit, Copy, Delete]
  };

  return initialList;
};

// 权限检查
export const authCheck = (privileges: string[], auth: string) => {
  if (!privileges || !isArray(privileges)) {
    return false;
  }

  // 管理权限
  if (privileges.includes(Privilege.Manage)) {
    return true;
  }

  // 没有管理权限 但是需要管理权限
  if (auth === Privilege.Manage) {
    return false;
  }

  return privileges.includes(auth);
};
