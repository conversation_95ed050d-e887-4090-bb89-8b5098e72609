import {JobConfigStatus} from '@api/integration/type';

/** 数据集成 - 结构化 - 采集类型 离线采集、实时采集 */
export enum SqlCollectTabEnum {
  Offline = 'offline',
  Realtime = 'realtime'
}
export const SqlCollectTabMap = {
  [SqlCollectTabEnum.Offline]: {
    label: '离线采集',
    value: SqlCollectTabEnum.Offline
  },
  [SqlCollectTabEnum.Realtime]: {
    label: '实时采集',
    value: SqlCollectTabEnum.Realtime
  }
};

/** 数据集成 - 结构化 - 源端类型 */
export enum SourceTypeEnum {
  MySQL = 'MySQL',
  Oracle = 'Oracle',
  SQLServer = 'SQLServer',
  PostgreSQL = 'PostgreSQL',
  Hana = 'HANA'
  // KingBaseES = 'KingBase ES',
  // TDEngine = 'TDEngine',
  // influxDB = 'influxDB'
}

/** 数据集成 - 结构化 - 源端类型 */
export const SourceTypeMap = {
  [SourceTypeEnum.MySQL]: {
    label: 'MySQL',
    icon: 'mysql',
    value: SourceTypeEnum.MySQL,
    disabled: false
  },
  [SourceTypeEnum.Oracle]: {
    label: 'Oracle',
    icon: 'oracle',
    value: SourceTypeEnum.Oracle,
    disabled: false
  },
  [SourceTypeEnum.SQLServer]: {
    label: 'SQLServer',
    icon: 'sqlserver',
    value: SourceTypeEnum.SQLServer,
    disabled: false
  },
  [SourceTypeEnum.PostgreSQL]: {
    label: 'PostgreSQL',
    icon: 'postgresql',
    value: SourceTypeEnum.PostgreSQL,
    disabled: false
  },
  [SourceTypeEnum.Hana]: {
    label: 'HANA',
    icon: 'hana',
    value: SourceTypeEnum.Hana,
    disabled: false
  }
  // [SourceTypeEnum.KingBaseES]: {
  //   label: 'KingBase ES',
  //   icon: 'kingbasees',
  //   disabled: true
  // },
  // [SourceTypeEnum.TDEngine]: {
  //   label: 'TDEngine',
  //   icon: 'tdengine',
  //   disabled: true
  // },
  // [SourceTypeEnum.influxDB]: {
  //   label: 'influxDB',
  //   icon: 'influxdb',
  //   disabled: true
  // }
};

/** 数据集成 - 结构化 - 目的端 */
export enum TargetTypeEnum {
  DataBuilderCatalog = 'Data Builder Catalog'
}

/** 数据集成 - 结构化 - 目的端类型 */
export enum SinkTypeEnum {
  iceberg = 'iceberg',
  doris = 'doris'
}
// 数据集成 - 结构化 - 目的端类型 字段类型可以选择的
export const DbTypeObject = {
  [SinkTypeEnum.iceberg]: {
    BOOLEAN: 'BOOLEAN',
    INT: 'INT',
    LONG: 'LONG',
    FLOAT: 'FLOAT',
    DOUBLE: 'DOUBLE',
    DATE: 'DATE',
    TIMESTAMP: 'TIMESTAMP',
    TIMESTAMP_NTZ: 'TIMESTAMP_NTZ',
    STRING: 'STRING',
    BINARY: 'BINARY',
    DECIMAL: 'DECIMAL'
  },
  [SinkTypeEnum.doris]: {
    CHAR: 'CHAR',
    VARCHAR: 'VARCHAR',
    STRING: 'STRING',
    TINYINT: 'TINYINT',
    SMALLINT: 'SMALLINT',
    INT: 'INT',
    BIGINT: 'BIGINT',
    LARGEINT: 'LARGEINT',
    FLOAT: 'FLOAT',
    DOUBLE: 'DOUBLE',
    DECIMAL: 'DECIMAL',
    BOOLEAN: 'BOOLEAN',
    DATE: 'DATE',
    DATETIME: 'DATETIME'
  }
};
/** 数据集成 - 结构化 - 目的端类型 字段类型映射 */
export const DbTypeMap = {
  [SinkTypeEnum.iceberg]: {
    BOOLEAN: DbTypeObject[SinkTypeEnum.iceberg].BOOLEAN,
    BYTE: DbTypeObject[SinkTypeEnum.iceberg].INT,
    SHORT: DbTypeObject[SinkTypeEnum.iceberg].INT,
    INT: DbTypeObject[SinkTypeEnum.iceberg].INT,
    LONG: DbTypeObject[SinkTypeEnum.iceberg].LONG,
    FLOAT: DbTypeObject[SinkTypeEnum.iceberg].FLOAT,
    DOUBLE: DbTypeObject[SinkTypeEnum.iceberg].DOUBLE,
    DATE: DbTypeObject[SinkTypeEnum.iceberg].DATE,
    TIMESTAMP: DbTypeObject[SinkTypeEnum.iceberg].TIMESTAMP,
    TIMESTAMP_NTZ: DbTypeObject[SinkTypeEnum.iceberg].TIMESTAMP_NTZ,
    CHAR: DbTypeObject[SinkTypeEnum.iceberg].STRING,
    VARCHAR: DbTypeObject[SinkTypeEnum.iceberg].STRING,
    STRING: DbTypeObject[SinkTypeEnum.iceberg].STRING,
    BINARY: DbTypeObject[SinkTypeEnum.iceberg].BINARY,
    DECIMAL: DbTypeObject[SinkTypeEnum.iceberg].DECIMAL
  },
  [SinkTypeEnum.doris]: {
    BOOLEAN: DbTypeObject[SinkTypeEnum.doris].BOOLEAN,
    BYTE: DbTypeObject[SinkTypeEnum.doris].TINYINT,
    SHORT: DbTypeObject[SinkTypeEnum.doris].SMALLINT,
    INT: DbTypeObject[SinkTypeEnum.doris].INT,
    LONG: DbTypeObject[SinkTypeEnum.doris].BIGINT,
    FLOAT: DbTypeObject[SinkTypeEnum.doris].FLOAT,
    DOUBLE: DbTypeObject[SinkTypeEnum.doris].DOUBLE,
    DATE: DbTypeObject[SinkTypeEnum.doris].DATE,
    TIMESTAMP: DbTypeObject[SinkTypeEnum.doris].DATETIME,
    TIMESTAMP_NTZ: DbTypeObject[SinkTypeEnum.doris].DATETIME,
    CHAR: DbTypeObject[SinkTypeEnum.doris].CHAR,
    VARCHAR: DbTypeObject[SinkTypeEnum.doris].VARCHAR,
    STRING: DbTypeObject[SinkTypeEnum.doris].STRING,
    BINARY: DbTypeObject[SinkTypeEnum.doris].STRING,
    DECIMAL: DbTypeObject[SinkTypeEnum.doris].DECIMAL
  }
};

/** 数据集成 - 结构化 - 目的端表类型 */
export enum SinkTableTypeEnum {
  MANAGED = 'MANAGED',
  EXTERNAL = 'EXTERNAL'
}

/** 数据集成 - 结构化 - 目的端表名称规则 */
export const SinkTableTypeChineseMap = {
  [SinkTableTypeEnum.MANAGED]: '内部表',
  [SinkTableTypeEnum.EXTERNAL]: '外部表'
};

/** 数据集成 - 结构化 - 目的端表名称规则 */
export enum SinkNameRuleEnum {
  SAME = 'SAME',
  ADDPREFIX = 'ADD_PREFIX',
  ADDSUFFIX = 'ADD_SUFFIX',
  ADDPREFIXANDSUFFIX = 'ADD_PREFIX_AND_SUFFIX'
}

/** 数据集成 - 结构化 - 目的端表名称规则 */
export const SinkNameRuleChineseMap = {
  [SinkNameRuleEnum.SAME]: '无前后缀',
  [SinkNameRuleEnum.ADDPREFIX]: '增加前缀',
  [SinkNameRuleEnum.ADDSUFFIX]: '增加后缀',
  [SinkNameRuleEnum.ADDPREFIXANDSUFFIX]: '增加前后缀'
};

/** 数据集成 - 结构化 - 脏数据策略 */
export enum DirtyDataStrategyEnum {
  STRICT = 'STRICT',
  TOLERANT = 'TOLERANT',
  IGNORE = 'IGNORE'
}

/** 数据集成 - 结构化 - 脏数据策略 */
export const DirtyDataStrategyChineseMap = {
  [DirtyDataStrategyEnum.STRICT]: '不容忍脏数据',
  [DirtyDataStrategyEnum.TOLERANT]: '容忍部分脏数据',
  [DirtyDataStrategyEnum.IGNORE]: '忽略脏数据'
};

/** 数据集成 - 结构化 - 脏数据处理方式 */
export enum DirtyDataHandleEnum {
  SKIP = 'SKIP',
  OVERWRITE = 'OVERWRITE',
  ABORT = 'ABORT'
}

/** 数据集成 - 结构化 - 分片字段类型 */
export enum SplitFieldTypeEnum {
  TINYINT = 'TINYINT',
  SMALLINT = 'SMALLINT',
  INTEGER = 'INTEGER',
  BIGINT = 'BIGINT',
  REAL = 'REAL',
  FLOAT = 'FLOAT',
  DOUBLE = 'DOUBLE',
  NUMERIC = 'NUMERIC',
  DECIMAL = 'DECIMAL',
  BIT = 'BIT',
  BOOLEAN = 'BOOLEAN',
  DATE = 'DATE',
  TIME = 'TIME',
  TIMESTAMP = 'TIMESTAMP'
}

/** 数据集成 - 结构化 - 源端变更 处理逻辑 */
export enum SourceChangeHandleEnum {
  SKIP = 'SKIP',
  PAUSE = 'PAUSE',
  ADD = 'ADD'
}

/** 数据集成 - 结构化 - 源端变更 处理逻辑 */
export const SourceChangeHandleChineseMap = {
  [SourceChangeHandleEnum.PAUSE]: '终止任务',
  [SourceChangeHandleEnum.SKIP]: '忽略已删除字段'
};

export const SourceADDHandleChineseMap = {
  [SourceChangeHandleEnum.PAUSE]: '终止任务',
  [SourceChangeHandleEnum.ADD]: '忽略已新增字段'
};

/** 数据集成 - 结构化 - 写入模式 */
export enum SinkModeEnum {
  OVERWRITE = 'overwrite',
  APPEND = 'append',
  UPSERT = 'upsert'
}

/** 数据集成 - 结构化 - 写入模式 */
export const SinkModeChineseMap = {
  [SinkModeEnum.OVERWRITE]: 'overwrite(覆盖写入)',
  [SinkModeEnum.APPEND]: 'append(追加写入)',
  [SinkModeEnum.UPSERT]: 'upsert(更新写入)'
};

/** 数据集成 - 结构化 - 脏数据容忍度单位 */
export enum DirtyDataToleranceUnitEnum {
  PERCENT = 'PERCENT',
  ROWS = 'ROWS'
}

/** 数据集成 - 结构化 - 脏数据容忍度单位 */
export const DirtyDataToleranceUnitChineseMap = {
  [DirtyDataToleranceUnitEnum.PERCENT]: '百分比',
  [DirtyDataToleranceUnitEnum.ROWS]: '行数'
};

// 数据集成 - 结构化 - 配置项
export enum SourceConfigEnum {
  SourceConfig = 'sourceConfig',
  SinkConfig = 'sinkConfig',
  MappingConfig = 'mappingConfig'
}

/** 数据集成 - 结构化 - jsPlumb id */
export enum JsPlumbIdEnum {
  SOURCE = 'js-plumb-source-',
  TARGET = 'js-plumb-target-'
}

export enum PartitioningFunctionEnum {
  year = 'year',
  month = 'month',
  day = 'day',
  hour = 'hour',
  bucket = 'bucket',
  truncate = 'truncate',
  identity = 'identity'
}

// 操作展示形式，列表内、列表内下拉，详情内下拉
export enum OperationShowType {
  List = 'list',
  Dropdown = 'dropdown',
  DetailList = 'detailList',
  DetailDropdown = 'detailDropdown',
  ResultDropdown = 'resultDropdown'
}

// 任务状态配置
export const JobStatusConfigMap = {
  [JobConfigStatus.Draft]: {
    label: '草稿',
    bgColor: '#070C140F',
    color: '#5C5F66',
    icon: 'status-inactive'
  },
  [JobConfigStatus.PreCheck]: {
    label: '前置检查中',
    bgColor: '#E6F0FF',
    color: '#2468F2',
    icon: 'status-processing'
  },
  [JobConfigStatus.CheckPass]: {
    label: '前置检查通过',
    bgColor: '#E6F0FF',
    color: '#2468F2',
    icon: 'status-success'
  },
  [JobConfigStatus.CheckFail]: {
    label: '前置检查未通过',
    bgColor: '#FFE8E6',
    color: '#F33E3E',
    icon: 'status-error'
  },
  [JobConfigStatus.Updating]: {
    label: '更新中',
    bgColor: '#E6F0FF',
    color: '#2468F2',
    icon: 'status-processing'
  },
  [JobConfigStatus.Published]: {
    label: '已发布',
    bgColor: '#ECFFE6',
    color: '#30BF13',
    icon: 'status-success'
  }
};

// 操作方法
export enum Operation {
  PreCheck = 'preCheck',
  Start = 'start',
  Publish = 'publish',
  Copy = 'copy',
  Delete = 'delete',
  Edit = 'edit'
}
