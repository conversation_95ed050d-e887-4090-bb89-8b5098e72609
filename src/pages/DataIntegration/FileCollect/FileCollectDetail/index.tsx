import useUrlState from '@ahooksjs/use-url-state';
import urls from '@utils/urls';
import {Breadcrumb, Button, Dropdown, Link, Loading, Menu, Space, Tabs, Tooltip} from 'acud';
import React, {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';
import * as http from '@api/integration';
import {WorkspaceContext} from '@pages/index';
import {JobDetailRes} from '@api/integration/type';

import styles from './index.module.less';
import IconSvg from '@components/IconSvg';
import {DataSourceMap, DetailTab, OperationShowType} from '../constants';
import FileCollectDetailConfig from '../components/FileCollectDetailConfig';
import ProcessLog from '../components/ProcessLog';
import {getMetaUrl, getOperationList} from '../utils';
import {useNavigate} from 'react-router-dom';
import moment from 'moment';
import classNames from 'classnames';
import AuthButton from '@components/AuthComponents/AuthButton';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {JobStatusConfigMap} from '../constants';
import cx from 'classnames';

const {TabPane} = Tabs;

/**
 * 文件采集-详情
 */
export const FileCollectDetail: React.FC = () => {
  const navigate = useNavigate();
  const [urlState, setUrlState] = useUrlState({jobId: '', isPublished: false, tab: DetailTab.ProcessLog});
  const {workspaceId} = useContext(WorkspaceContext);
  const [detail, setDetail] = useState<JobDetailRes>();
  const [loading, setLoading] = useState<boolean>(false);
  const listRef = useRef();

  const getDetails = useCallback(async () => {
    setLoading(true);
    try {
      const res = await http.getJobDetails(workspaceId, urlState.jobId, {isPublished: urlState.isPublished});

      setDetail(res.result);
    } catch (err) {
      console.error(err);
    } finally {
      setLoading(false);
    }
  }, [urlState.jobId, urlState.isPublished, workspaceId]);

  useEffect(() => {
    getDetails();
  }, [getDetails]);

  const onTabChange = (activeKey) => setUrlState({tab: activeKey});

  const refresh = () => {
    (listRef.current as any).refresh();
  };

  // 渲染操作项
  const renderOperation = () => {
    const operations = getOperationList(navigate, {
      privileges: detail?.privileges,
      status: detail?.status
    });
    const jobs = [{jobId: urlState.jobId, name: detail?.name, sourceType: detail?.sourceConfig?.sourceType}];
    const menu = (
      <Menu>
        {operations[OperationShowType.DetailDropdown].map((item, index) => (
          <AuthMenuItem
            key={index}
            onClick={() => item.callback(jobs, workspaceId, refresh)}
            isAuth={item.isAuth}
            disabled={item.disabled}
          >
            {item.label}
          </AuthMenuItem>
        ))}
      </Menu>
    );
    return (
      <Space>
        <Dropdown overlay={menu}>
          <Button>
            <IconSvg type="more" size={16} color="#6c6d70" />
          </Button>
        </Dropdown>
        {operations[OperationShowType.DetailList].map((item, index) => (
          <AuthButton
            type="primary"
            key={index}
            onClick={() => item.callback(jobs, workspaceId, refresh)}
            isAuth={item.isAuth}
            disabled={item.disabled}
          >
            {item.label}
          </AuthButton>
        ))}
      </Space>
    );
  };

  const tabsList = useMemo(
    () => [
      {
        label: '运行记录',
        key: DetailTab.ProcessLog,
        render: <ProcessLog ref={listRef} privilege={detail?.privileges} />
      },
      {
        label: '任务信息',
        key: DetailTab.DetailConfig,
        render: <FileCollectDetailConfig detail={detail} />
      }
    ],
    [detail]
  );

  return (
    <div className={styles['info-container']}>
      <Breadcrumb>
        <Breadcrumb.Item>
          <Link href={`#${urls.integration}`}>文件采集</Link>
        </Breadcrumb.Item>
        <Breadcrumb.Item>任务名称</Breadcrumb.Item>
      </Breadcrumb>
      <Loading loading={loading}>
        <div className={styles['info-top']}>
          <div className={cx(styles['info-title'], 'flex', 'items-center')}>
            <Tooltip title={detail?.name}>{detail?.name}</Tooltip>
            <div
              className={cx(styles['info-status'], 'flex', 'items-center', 'ml-[12px]')}
              style={{
                background: JobStatusConfigMap[detail?.status]?.bgColor,
                color: JobStatusConfigMap[detail?.status]?.color
              }}
            >
              {JobStatusConfigMap[detail?.status]?.label || '-'}
            </div>
          </div>
          <div>{renderOperation()}</div>
        </div>
        <div className={styles['info-bottom']}>
          <div className={styles['source-name']}>
            <IconSvg
              type={DataSourceMap[detail?.sourceConfig?.sourceType]?.icon}
              color={DataSourceMap[detail?.sourceConfig?.sourceType]?.color}
              className="bordered-circle-icon mr-[8px]"
              size={14}
            />
            <Link
              onClick={() =>
                navigate(
                  `${urls.connectionDetail}?name=${detail?.sourceConfig?.sourceConnectionId}&workspaceId=${workspaceId}`
                )
              }
            >
              {detail?.sourceConfig?.sourceConnectionId}
            </Link>
          </div>
          <IconSvg type="right" size={16} />
          <div className={styles['dest-name']}>
            <Tooltip title={detail?.sinkConfig?.sinkVolume}>
              <Link
                onClick={() => navigate(getMetaUrl(detail?.sinkConfig?.sinkVolume, workspaceId))}
                className="text-ellipsis"
              >
                {detail?.sinkConfig?.sinkVolume}
              </Link>
            </Tooltip>
          </div>
          <div className={styles['info-text']}>
            <IconSvg type="time" size={16} className="mr-[6px]" color="#84868C" fill="transparent" />
            {moment(detail?.createTime).format('YYYY-MM-DD HH:mm:ss')}
          </div>
          <div className={styles['info-text']}>
            <IconSvg type="user" size={16} className="mr-[6px]" color="#84868C" fill="transparent" />
            {detail?.creatorName}
          </div>
          <div className={classNames(styles['info-text'], styles['info-text-ellipsis'])}>
            <IconSvg type="description" size={16} className="mr-[6px]" color="#84868C" fill="transparent" />
            <Tooltip title={detail?.description}>{detail?.description}</Tooltip>
          </div>
        </div>
        <Tabs onChange={onTabChange} activeKey={urlState.tab}>
          {tabsList.map((item) => (
            <TabPane tab={item.label} key={item.key}>
              {item.render}
            </TabPane>
          ))}
        </Tabs>
      </Loading>
    </div>
  );
};

export default FileCollectDetail;
