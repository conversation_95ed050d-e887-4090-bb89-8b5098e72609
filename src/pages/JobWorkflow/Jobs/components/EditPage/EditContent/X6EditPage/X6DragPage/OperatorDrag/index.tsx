import {Graph} from '@antv/x6';
import React, {useContext, useState} from 'react';

import {Dnd} from '@antv/x6-plugin-dnd';

import {IOperVersionOneRes} from '@api/metaRequest';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import IconSvg from '@components/IconSvg';
import DescriptionList from '@pages/JobInstance/ResultPage/components/DescriptionList';
import {JobNodeTypeEnum, NODE_SIZE, X6ShapeTypeEnum} from '@pages/JobWorkflow/constants';
import {WorkspaceContext} from '@pages/index';
import {IAppState} from '@store/index';
import urls from '@utils/urls';
import {Popover, Tree} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {useSelector} from 'react-redux';
import {dealZIndex} from '../../tools';
import {getNewId, getNewOperatorName} from '../../tools/copy';
import {handleMouseDown} from '../clickFn';
import HeaderFilter from './HeaderFilter';
import styles from './index.module.less';

/**
 * 处理算子详情
 * @param operator 算子
 * @returns 算子详情
 */
export const dealOperatorDetail = (operator: IOperVersionOneRes, workspaceId: string) => {
  return [
    {
      label: '算子名称',
      value: (
        <div className="flex gap-1 items-center h-[20px]">
          <Ellipsis tooltip={operator.operatorName}> {operator.operatorName}</Ellipsis>

          <IconSvg
            size={16}
            color="#2468f2"
            className="cursor-pointer"
            onClick={() =>
              window.open(
                window.location.pathname +
                  `#${urls.metaData}?catalog=${operator.catalogName}&node=${operator.operatorName}&refresh=true&schema=${operator.schemaName}&type=operator&workspaceId=${workspaceId}`,
                '_blank'
              )
            }
            type="open"
          ></IconSvg>
        </div>
      )
    },
    {
      label: '算子描述',
      value: operator.comment
    },
    {
      label: '可运行引擎',
      value: operator.engineType.join(',')
    },
    // {
    //   label: '算子标识',
    //   value: operator.operatorName
    // },
    {
      label: '算子版本',
      value: operator.name
    },
    {
      label: '输入参数',
      value: operator.input.map((item) => (
        <p key={String(item)}>
          {item.name}：{item.type}
        </p>
      ))
    },
    {
      label: '输出参数',
      value: operator.output.map((item) => (
        <p key={String(item)}>
          {item.name}：{item.type}
        </p>
      ))
    }
  ];
};

//定义类型
interface IX6DragProps {
  graph?: React.RefObject<Graph>;
  dnd?: React.RefObject<Dnd>;
  ref?: React.RefObject<HTMLDivElement>;
}

export interface IOperatorTreeData {
  title: string | React.ReactNode;
  key: string;
  value?: string;
  children?: IOperatorTreeData[];
}

// 算子拖拽组件
const OperatorDrag: React.FC<IX6DragProps> = ({graph, dnd, ref}) => {
  const operateParentId = useSelector((state: IAppState) => state.workflowSlice.operateParentId);

  const {workspaceId} = useContext(WorkspaceContext);
  const [treeData, setTreeData] = useState<IOperatorTreeData[]>([]);

  // 拖拽组件节点
  const startDrag = useMemoizedFn(
    (e: React.MouseEvent<HTMLDivElement, MouseEvent>, operator: IOperVersionOneRes) => {
      const id = getNewId(operateParentId);
      const name = getNewOperatorName(operator.operatorAlias, operateParentId);

      const cell = graph?.current.getCellById(operateParentId);
      cell?.setData({
        isExpanded: true
      });
      dealZIndex(cell, graph?.current);
      // 初始化数据
      const node = graph?.current?.createNode({
        id,
        shape: X6ShapeTypeEnum.OPERATOR,
        data: {
          name: name,
          parentId: operateParentId,
          type: JobNodeTypeEnum.OPERATOR_NODE,
          params: {
            name: name,
            id: id.slice(operateParentId.length + 1),
            params: operator?.execParams?.map((item) => {
              return {
                key: item.name,
                value: item.defaultValue,
                valueType: item.type
              };
            }),
            metaData: {
              catalogName: operator?.catalogName,
              schemaName: operator?.schemaName,
              operatorName: operator?.operatorName,
              version: operator?.name
            },
            location: {}
          }
        },
        width: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].width,
        height: NODE_SIZE[X6ShapeTypeEnum.OPERATOR].height
      });

      handleMouseDown(e, node);

      dnd?.current?.start(node, e.nativeEvent as any);
    }
  );
  function stopAll(e) {
    e.preventDefault?.();
    e.stopPropagation();
  }

  // 处理算子节点
  const dealOperatorNode = (item: IOperVersionOneRes, filterName: string): IOperatorTreeData => {
    // filterName
    return {
      ...item,
      title: (
        <div
          className={styles['operator-name']}
          data-type={item.id}
          onMouseDown={(e) => startDrag(e, item as any)}
        >
          <IconSvg type="workflow-operator" size={16} fill="none" />
          <Ellipsis tooltip={item.operatorAlias}>{item.operatorAlias}</Ellipsis>

          <Popover
            content={
              <div
                style={{width: 300}}
                // 避免点击事件穿透到上层元素，导致误操作
                onClick={stopAll}
                onMouseDown={stopAll}
                onMouseUp={stopAll}
                onDoubleClick={stopAll}
                onDragStart={stopAll}
                onDragEnter={stopAll}
                onDragOver={stopAll}
                onDragLeave={stopAll}
                onDrop={stopAll}
                onDragEnd={stopAll}
              >
                <DescriptionList
                  flex="0 0 70px"
                  infoList={dealOperatorDetail(item, workspaceId)}
                  colon={false}
                />
              </div>
            }
            trigger="hover"
          >
            <IconSvg className={styles['operator-view']} type="workflow-view" size={16} fill="none" />
          </Popover>
        </div>
      ),
      key: item.id
    };
  };

  return (
    <div className={styles['dnd-content']} ref={ref}>
      <HeaderFilter changeOperatorTree={setTreeData} dealOperatorNode={dealOperatorNode}></HeaderFilter>

      <div
        className={`${styles['operator-name']} ${styles['custom-operator-name']}  `}
        data-type={JobNodeTypeEnum.OPERATOR_NODE}
        onMouseDown={(e) =>
          startDrag(e, {
            operatorAlias: '自定义算子'
          } as any)
        }
      >
        <IconSvg type="workflow-operator" size={16} fill="none" />
        自定义算子
      </div>
      <Tree treeData={treeData} defaultExpandAll={true}></Tree>
    </div>
  );
};

export default React.memo(OperatorDrag);
