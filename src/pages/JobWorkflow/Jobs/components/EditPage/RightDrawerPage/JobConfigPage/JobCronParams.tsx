import {ICronForm} from '@api/job';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {
  JobPriorityChineseMap,
  JobPriorityEnum,
  JobScheduleStatusChinese,
  JobScheduleStatusEnum
} from '@pages/JobWorkflow/constants';
import JobCronFormItem from '@pages/JobWorkflow/Jobs/components/FormItem/JobCronFormItem';
import {dealCronToForm, dealFormToCron} from '@pages/JobWorkflow/tools';
import {IAppState} from '@store/index';
import {setFormIsDirty} from '@store/workflow';
import {DatePicker, Form, Select} from 'acud';
import moment from 'moment';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import JobCronShowTimeItem from '../../../FormItem/JobCronShowTimeItem';
import {jobData} from '../../globalVar';
const JobCronParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const [scheduleStatus, setScheduleStatus] = useState<JobScheduleStatusEnum>(JobScheduleStatusEnum.OFF);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  // 初始化表单
  useEffect(() => {
    setScheduleStatus(jobData.value.scheduleStatus);

    const scheduleConf = jobData.value.scheduleConf;
    const cronForm = dealCronToForm(scheduleConf);

    form.setFieldsValue({...cronForm, priority: jobData.value.priority});
    setTimeout(() => {
      form.validateFields();
    }, 0);
  }, []);

  // 修改表单 更新全局变量
  const changeForm = (_: any, values: ICronForm & {priority: JobPriorityEnum}) => {
    const scheduleConf = dealFormToCron(values);
    jobData.value.scheduleConf = scheduleConf;
    jobData.value.priority = values.priority;
    console.log('scheduleConf', scheduleConf);
    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  return (
    <>
      <div className={'form-title'}>
        <IconSvg size={16} type="workflow-detail" />
        <span className={'form-title-text'}>策略信息</span>
      </div>

      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="global"
        form={form}
        className="mb-2"
        labelWidth={70}
        onValuesChange={changeForm}
        colon={false}
      >
        <Form.Item
          label="优先级"
          name="priority"
          tooltip="提示：工作流在调度过程中的执行优先级，级别高的流程在执行队列中会优先执行，相同优先级的流程按照先进先出的顺序执行。"
        >
          <EditableContent isEditing={isEditing} dealValue={(value) => JobPriorityChineseMap[value]}>
            <Select
              className="w-full"
              options={Object.entries(JobPriorityChineseMap).map(([key, value]) => ({
                value: key,
                label: value
              }))}
            />
          </EditableContent>
        </Form.Item>
        <Form.Item label="调度状态">{JobScheduleStatusChinese[scheduleStatus]}</Form.Item>
        <Form.Item label="起始时间" name="startTime">
          <EditableContent isEditing={isEditing} dealValue={(value) => value?.format('YYYY-MM-DD HH:mm:ss')}>
            <DatePicker
              className="w-full"
              disabled={!isEditing}
              showTime
              showNow={false}
              clearIcon={false}
              disabledDate={(current) => current > moment(form.getFieldValue('endTime'))}
            />
          </EditableContent>
        </Form.Item>

        <Form.Item label="终止时间" name="endTime">
          <EditableContent isEditing={isEditing} dealValue={(value) => value?.format('YYYY-MM-DD HH:mm:ss')}>
            <DatePicker
              className="w-full"
              disabled={!isEditing}
              showTime
              showNow={false}
              clearIcon={false}
              disabledDate={(current) => current < moment(form.getFieldValue('startTime'))}
            />
          </EditableContent>
        </Form.Item>

        <JobCronFormItem disabled={!isEditing} form={form} onChange={changeForm} />
      </Form>

      <JobCronShowTimeItem form={form} />
    </>
  );
};

export default JobCronParams;
