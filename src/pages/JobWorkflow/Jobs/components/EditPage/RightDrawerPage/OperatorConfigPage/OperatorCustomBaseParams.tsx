import {EntityType, getOperator<PERSON><PERSON><PERSON>ist, IOperVersionOneRes} from '@api/metaRequest';
import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {SPLIT_STR} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setEditNodeData, setFormIsDirty} from '@store/workflow';
import {Button, Form, Input, Select, Tooltip} from 'acud';
import React, {useContext, useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {IJsonOperatorData} from '../../EditContent/X6EditPage/type';
import {nodeMap, operatorDetailMap} from '../../globalVar';
import FilePathSelectFormItem, {MetaType, PathLevel} from '@components/FilePathSelectFormItem';
import {WorkspaceContext} from '@pages/index';
import {useMemoizedFn} from 'ahooks';

const OperatorCustomBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  const {workspaceId} = useContext(WorkspaceContext);
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [closeModalTime, setCloseModalTime] = useState(0);
  // 算子参数
  const [detail, setDetail] = useState<IOperVersionOneRes | null>(null);
  // 算子版本列表
  const [versionList, setVersionList] = useState<IOperVersionOneRes[]>([]);
  // 节点参数
  const [nodeParams, setNodeParams] = useState<IJsonOperatorData | null>(null);
  // 初始化表单
  useEffect(() => {
    const selectedNode = nodeMap.get(selectedNodeId) as IJsonOperatorData;
    form.setFieldsValue(selectedNode);
    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId);
    const newObj = {...oldObj, ...values};
    nodeMap.set(selectedNodeId, newObj);

    dispatch(setEditNodeData({id: selectedNodeId, name: values.name}));

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  const setFormValues = (key: string, value: any) => {
    form.setFieldValue(key, value);
    changeForm(null, {[key]: value});
  };

  const fullName = Form.useWatch('fullName', form);
  const version = Form.useWatch(['metaData', 'version'], form);

  // 处理参数
  const dealParams = useMemoizedFn(() => {
    if (fullName && version) {
      // 获取已有数据参数
      const selectedNode = nodeMap.get(selectedNodeId) as IJsonOperatorData;
      const operatorDetail = versionList.filter((item) => item.name === version)[0];
      const params = [];
      operatorDetail?.execParams.forEach((item) => {
        params.push({
          key: item.name,
          value: selectedNode?.params?.find((p) => p.key === item.name)?.value || item.defaultValue,
          valueType: item.type
        });
      });

      form.setFieldsValue({params});
      setDetail(operatorDetail);
    }
  });

  useEffect(() => {
    dealParams();
  }, [fullName, version, versionList, dealParams]);

  const initOperatorVersionList = useMemoizedFn(async () => {
    const res = await getOperatorAllList(workspaceId, fullName, {pageNo: 1, pageSize: 100});
    console.log(res);
    const versionList = res.result.versions;
    setVersionList(versionList);

    const [catalogName, schemaName, operatorName] = fullName.split('.');
    setFormValues('metaData', {
      catalogName,
      schemaName,
      operatorName,
      version: version ? version : versionList[0]?.name
    });
    dealParams();
  });
  useEffect(() => {
    if (fullName) {
      initOperatorVersionList();
    }
  }, [fullName, initOperatorVersionList]);

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        colon={false}
        name="basic"
        form={form}
        labelWidth={70}
        onValuesChange={changeForm}
      >
        <div className={'form-title'}>
          <IconSvg size={16} type="job-instance-result" />
          <span className={'form-title-text'}>算子信息</span>
        </div>
        {/* 算子配置 */}
        <Form.Item name={['metaData', 'catalogName']} noStyle hidden />
        <Form.Item name={['metaData', 'schemaName']} noStyle hidden />
        <Form.Item name={['metaData', 'operatorName']} noStyle hidden />

        <Form.Item
          label="算子节点名称"
          name="name"
          rules={[{required: isEditing ? true : false, message: '请输入算子节点名称'}]}
        >
          <EditableContent isEditing={isEditing}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
          </EditableContent>
        </Form.Item>
        <FilePathSelectFormItem
          form={form}
          name={'fullName'}
          label="算子名称"
          placeholderReplace="<catalog>.<schema>.<operator>"
          searchType={[EntityType.CATALOG, EntityType.SCHEMA, EntityType.OPERATOR]}
          selectableLevel={PathLevel.Volume}
          latestLevel={PathLevel.Schema}
          metaDirs={['operator']}
          metaType={MetaType.Operator}
          closeModalTime={closeModalTime}
        />

        <Form.Item label="节点 ID">
          {nodeParams?.id}
          <Clipboard text={nodeParams?.id} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="算子组件标识">{detail?.category}</Form.Item>
        <Form.Item label="版本" name={['metaData', 'version']}>
          <Select
            options={versionList.map((item) => ({
              value: item.name,
              label: item.name
            }))}
          />
        </Form.Item>
        <div className={'form-title'}>
          <IconSvg size={16} type="job-instance-result" />
          <span className={'form-title-text'}>算子参数</span>
        </div>
        <Form.List name="params">
          {(fields) => (
            <>
              {fields.map(({name, key, ...restField}, index) => (
                <div key={key}>
                  <Form.Item hidden {...restField} name={[name, 'valueType']}>
                    <Input />
                  </Form.Item>
                  <Form.Item hidden {...restField} name={[name, 'key']}>
                    <Input />
                  </Form.Item>

                  <Form.Item
                    label={
                      <Tooltip
                        title={`${detail?.execParams[index]?.name}：${detail?.execParams[index]?.comment}`}
                      >
                        <div style={{width: '75px'}} className="truncate">
                          {detail?.execParams[index]?.name}
                        </div>
                      </Tooltip>
                    }
                    {...restField}
                    name={[name, 'value']}
                    rules={[
                      {
                        required: isEditing && detail?.execParams[index]?.required ? true : false,
                        message: '请输入' + detail?.execParams[index]?.name
                      }
                    ]}
                  >
                    <EditableContent isEditing={isEditing}>
                      <Input placeholder="请输入" />
                    </EditableContent>
                  </Form.Item>
                </div>
              ))}
            </>
          )}
        </Form.List>
      </Form>
    </>
  );
};

export default OperatorCustomBaseParams;
