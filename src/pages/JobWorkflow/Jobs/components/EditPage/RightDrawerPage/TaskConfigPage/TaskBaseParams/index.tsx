import {Clipboard} from '@baidu/bce-react-toolkit';
import EditableContent from '@components/EditableContent';
import IconSvg from '@components/IconSvg';
import {JobNodeTypeEnum, JobTaskType} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {setEditNodeData, setFormIsDirty} from '@store/workflow';
import {Button, Form, Input, InputNumber, Radio} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {cloneDeep} from 'lodash';
import React, {useEffect, useState} from 'react';
import {useDispatch, useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../globalVar';
import TaskParam from './TaskParam';
import AlertStrategyList from './components/formItem/AlertStrategyList/AlertStrategyList';

const TaskBaseParams: React.FC = () => {
  // 是否编辑
  const isEditing = useSelector((state: IAppState) => state.workflowSlice.isEditing);
  // 选中的 id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);
  const [form] = Form.useForm();
  const dispatch = useDispatch();
  const [detail, setDetail] = useState<IJsonNodeData | null>(null);
  // 初始化表单
  useEffect(() => {
    if (!selectedNodeId) {
      return;
    }
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const objClone = cloneDeep(obj);
    setDetail(objClone);
    // 设置表单
    form.setFieldsValue(objClone);

    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  // 修改表单 更新全局变量
  const changeForm = async (_: any, values: any) => {
    const oldObj = nodeMap.get(selectedNodeId);
    const newObj = {
      ...oldObj,
      ...values
    };
    nodeMap.set(selectedNodeId, newObj);
    dispatch(setEditNodeData({id: selectedNodeId, name: values.name}));

    // 设置表单脏
    dispatch(setFormIsDirty(true));
  };

  // 修改任务参数表单
  const changeTaskParam = useMemoizedFn(async (key: string, value: any) => {
    form.setFieldsValue({
      [key]: value
    });
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      [key]: value
    };
    nodeMap.set(selectedNodeId, newObj);
  });
  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="basic"
        form={form}
        labelWidth={70}
        onValuesChange={changeForm}
        colon={false}
      >
        <div className={'form-title'}>
          <IconSvg size={16} type="workflow-detail" />
          <span className={'form-title-text'}>任务节点信息</span>
        </div>
        <Form.Item
          label="任务节点名称"
          name="name"
          rules={[{required: isEditing ? true : false, message: '请输入任务节点名称'}]}
        >
          <EditableContent isEditing={isEditing}>
            <Input forbidIfLimit={true} limitLength={256} placeholder="请输入" allowClear />
          </EditableContent>
        </Form.Item>
        <Form.Item label="任务节点 ID">
          {selectedNodeId}
          <Clipboard text={selectedNodeId} className={'inline-block'}>
            <Button icon={<IconSvg type="copy" size={14} />} type="actiontext" />
          </Clipboard>
        </Form.Item>
        <Form.Item label="任务组件">{JobTaskType[detail?.type]?.label}</Form.Item>
        <Form.Item label="描述" name="description">
          <EditableContent isEditing={isEditing}>
            <Input.TextArea forbidIfLimit={true} limitLength={500} placeholder="请输入描述" allowClear />
          </EditableContent>
        </Form.Item>

        <TaskParam />

        {detail?.type !== JobNodeTypeEnum.DEPENDENT_TASK && (
          <>
            <div className={'form-title'}>
              <IconSvg size={16} type="workflow-detail" />
              <span className={'form-title-text'}>重试策略</span>
            </div>
            <Form.Item
              noStyle
              shouldUpdate={(prevValues, curValues) => prevValues.failRetryTimes !== curValues.failRetryTimes}
            >
              {({getFieldValue}) => {
                const failRetryTimes = getFieldValue('failRetryTimes') ? '1' : '0';
                return (
                  <Form.Item label="是否重试">
                    {isEditing ? (
                      <Radio.Group
                        value={failRetryTimes}
                        onChange={(value: any) => {
                          changeTaskParam('failRetryTimes', Number(value.target.value));
                        }}
                        options={[
                          {
                            label: '是',
                            value: '1'
                          },
                          {
                            label: '否',
                            value: '0'
                          }
                        ]}
                      />
                    ) : (
                      <span>{failRetryTimes === '1' ? '是' : '否'}</span>
                    )}
                  </Form.Item>
                );
              }}
            </Form.Item>
            <Form.Item label="重试次数" name="failRetryTimes" initialValue={0}>
              <EditableContent isEditing={isEditing} dealValue={(value) => `${value} 次`}>
                <InputNumber className="w-full" min={1} precision={0} formatter={(value) => `${value} 次`} />
              </EditableContent>
            </Form.Item>
            <Form.Item label="重试间隔" name="failRetryInterval" initialValue={1}>
              <EditableContent isEditing={isEditing} dealValue={(value) => `${value} 分钟`}>
                <InputNumber
                  className="w-full"
                  min={1}
                  precision={0}
                  formatter={(value) => `${value} 分钟`}
                />
              </EditableContent>
            </Form.Item>
          </>
        )}
        {/* 等待8月份放开测试 */}
        {/* <Form.Item name="taskAlertStrategyList">
          <AlertStrategyList />
        </Form.Item> */}
      </Form>
    </>
  );
};

export default TaskBaseParams;
