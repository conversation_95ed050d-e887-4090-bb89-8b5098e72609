import {JobNodeTypeEnum} from '@pages/JobWorkflow/constants';
import {IAppState} from '@store/index';
import {Form} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {cloneDeep} from 'lodash';
import React, {useEffect, useMemo} from 'react';
import {useSelector} from 'react-redux';
import {IJsonNodeData} from '../../../EditContent/X6EditPage/type';
import {nodeMap} from '../../../globalVar';
import AihcTaskParams from './components/AihcTaskParams';
import DependencyTaskParams from './components/DependencyTaskParams';
import IntegrationTaskParams from './components/IntegrationTaskParams';
import NotebookParams from './components/NotebookParams';
import RayTaskParams from './components/RayTaskParams';
import SparkTaskParams from './components/SparkTaskParams';

// 值转表单
const valueToForm = (type: JobNodeTypeEnum, value: any) => {
  console.log(value);
  switch (type) {
    case JobNodeTypeEnum.RAY_TASK:
      if (!value?.envVars || value?.envVars?.length === 0) {
        value.envVars = [{key: '', value: ''}];
      }

      return {
        ...value
      };
    default:
      return value;
  }
};

// 表单转值
const formToValue = (type: JobNodeTypeEnum, value: any) => {
  switch (type) {
    case JobNodeTypeEnum.RAY_TASK:
      value.envVars = value.envVars?.filter((item) => item.key || item.value);
      break;
    default:
      break;
  }
};

const TaskParam: React.FC = () => {
  // 选中的 id
  const selectedNodeId = useSelector((state: IAppState) => state.workflowSlice.selectedNodeId);

  const selectedNode = useMemo(() => {
    return nodeMap.get(selectedNodeId) as IJsonNodeData;
  }, [selectedNodeId]);

  const [form] = Form.useForm();

  // 初始化表单
  useEffect(() => {
    if (!selectedNodeId) {
      return;
    }
    const obj = nodeMap.get(selectedNodeId) as IJsonNodeData;
    const objClone = cloneDeep(obj);
    console.log(objClone);

    const formValue = valueToForm(objClone.type, objClone.taskParam);
    // 设置表单
    if (objClone?.taskParam) {
      form.setFieldsValue(formValue);
    }

    // 等待更新后再校验 TODO 后期需要优化 目前 0 的话 还是会有时候没有校验
    setTimeout(() => {
      form.validateFields();
    }, 100);
  }, [selectedNodeId]);

  // 修改任务参数表单
  const changeTaskParamForm = useMemoizedFn(async (_: any, values: any) => {
    const valuesClone = cloneDeep(values);

    console.log(valuesClone);
    formToValue(selectedNode.type, valuesClone);
    const oldObj = nodeMap.get(selectedNodeId) as IJsonNodeData;

    const newObj = {
      ...oldObj,
      taskParam: {
        ...oldObj.taskParam,
        ...valuesClone
      }
    };
    nodeMap.set(selectedNodeId, newObj);
  });

  const TaskParamComponent = useMemo(() => {
    switch (selectedNode?.type) {
      case JobNodeTypeEnum.NOTEBOOK_TASK:
        return <NotebookParams form={form} />;
      case JobNodeTypeEnum.RAY_TASK:
        return <RayTaskParams form={form} />;
      case JobNodeTypeEnum.DEPENDENT_TASK:
        return <DependencyTaskParams form={form} />;
      case JobNodeTypeEnum.FILE_INTEGRATION_TASK:
      case JobNodeTypeEnum.TABLE_INTEGRATION_TASK:
        return <IntegrationTaskParams form={form} type={selectedNode?.type} key={selectedNode?.type} />;
      case JobNodeTypeEnum.SPARK_JAR_TASK:
        return <SparkTaskParams form={form} type="jar" />;
      case JobNodeTypeEnum.PY_SPARK_TASK:
        return <SparkTaskParams form={form} type="py" />;
      case JobNodeTypeEnum.AIHC_TASK:
        return <AihcTaskParams form={form} />;
      default:
        return null;
    }
  }, [selectedNode?.type, form]);

  return (
    <>
      <Form
        inputMaxWidth={'100%'}
        labelAlign="left"
        layout="horizontal"
        name="taskParam"
        form={form}
        labelWidth={70}
        colon={false}
        onValuesChange={changeTaskParamForm}
      >
        {TaskParamComponent}
      </Form>
    </>
  );
};

export default TaskParam;
