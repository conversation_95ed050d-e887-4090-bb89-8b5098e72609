import {Button, Drawer, Modal, Pagination, Space, Table, toast} from 'acud';
import {useMemoizedFn, useRequest} from 'ahooks';
import {useCallback, useContext, useEffect, useMemo, useRef, useState} from 'react';

import DescriptionEdit from '@components/DescriptionEdit';
import IconSvg from '@components/IconSvg';
import InfoPanel from '@components/InfoPanel';
import TextEllipsis from '@components/TextEllipsis';
import {useEventListener} from 'ahooks';

import * as http from '@api/metaRequest';
import {IUrlStateHandler} from '../index';

import {deleteOperator, deleteOperatorVersion} from '@api/meta/operate';
import {CatalogType} from '@api/metaRequest';
import {Privilege, ResourceType} from '@api/permission/type';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {useNavigate} from 'react-router-dom';
import PermissionManage from '../../../components/PermissionManage';
import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import EditOperatorModal from '../components/operator/EditOperatorModal';
import {MetaCnNameMap} from '../config';
import {isBuildInCatalog} from '../helper';
import AuthButton from '@components/AuthComponents/AuthButton';
import PermissionModal from '@components/Workspace/PermissionModal';

enum PanelEnum {
  OVERVIEW = '1',
  DETAIL = '2',
  PERMISSION = '3'
}
export const PRIVILEGE_LIST = [
  {label: '执行', value: 'EXECUTE'},
  {label: '新建版本', value: 'CREATE_OPERATOR_VERSION'},
  {label: '管理', value: 'MANAGE'}
];

const VersionDetailDrawer = ({
  visible,
  versionInfo,
  setVisible
}: {
  visible: boolean;
  versionInfo: any;
  setVisible: (visible: boolean) => void;
}) => {
  useEventListener('click', () => visible && setVisible(false), {target: document.body});

  const formatParam = (params: http.IOperInputOutput[]) =>
    params && params.length > 0 ? (
      <div>
        {params.map((param, index) => (
          <div className={index === 0 ? '' : 'mt-4'} key={index}>
            <span className="mr-2">字段名：{param.name}</span>
            <span className="mr-2">字段类型：{param.type}</span>
            {param.required && <span className="mr-2">是否必须：{param.required ? '是' : '否'}</span>}
            {param.defaultValue && <span className="mr-2">默认值：{param.defaultValue}</span>}
            {param.comment && <span>字段描述：{param.comment}</span>}
          </div>
        ))}
      </div>
    ) : null;
  const infoList = useMemo(() => {
    return [
      {
        label: '版本号',
        value: versionInfo.name
      },
      {
        label: '版本ID',
        value: versionInfo.id
      },
      {
        label: '版本描述',
        value: versionInfo.comment
      },
      {
        label: '创建人',
        value: versionInfo.createdBy
      },
      {
        label: '创建时间',
        value: versionInfo.createdAt
      },
      {
        label: '最近修改人',
        value: versionInfo.updatedBy
      },
      {
        label: '修改时间',
        value: versionInfo.updatedAt
      },
      {
        label: '代码语言',
        value: versionInfo.language
      },
      {
        label: '算子代码路径',
        value: versionInfo.storageLocation
      },
      {
        label: '算子类型',
        value: versionInfo.category
      },
      {
        label: '运行环境',
        value: versionInfo.runtimeEnv
      },
      {
        label: '输入参数',
        value: formatParam(versionInfo.input)
      },
      {
        label: '输出参数',
        value: formatParam(versionInfo.output)
      },
      {
        label: '运行参数',
        value: formatParam(versionInfo.execParams)
      },
      {
        label: '支持引擎',
        value: versionInfo.engineType
      },
      {
        label: '资源类型',
        value: versionInfo.resourceType
      }
    ];
  }, [versionInfo]);

  return (
    <Drawer
      className="work-meta-version-drawer"
      width={900}
      title="版本详情"
      visible={visible}
      closable
      onClose={() => setVisible(false)}
      mask={false}
    >
      <InfoPanel infoList={infoList} />
    </Drawer>
  );
};

const PanelOperator = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;
  const {catalog = '', schema = '', node = '', tab = PanelEnum.OVERVIEW} = urlState;

  const permissionModalRef = useRef<any>(null);
  const navigate = useNavigate();
  const initialPanes = useMemo(
    () => [
      {tab: '概览', key: PanelEnum.OVERVIEW},
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  const dropdownMenu = useMemo(
    () => [
      {
        key: 'rename',
        label: `重命名${MetaCnNameMap['Operator']}`,
        disable: !canWrite,
        authName: Privilege.Manage
      },
      {
        key: 'remove',
        label: `删除${MetaCnNameMap['Operator']}`,
        disable: !canWrite,
        authName: Privilege.Manage
      }
    ],
    [canWrite]
  );

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // operator 全名
  const fullName = `${catalog}.${schema}.${node}`;

  // 详情 & 概览
  const [dataInfo, setDataInfo] = useState<http.IOperatorDetailRes>();

  // 分页字段
  const [pagination, setPagination] = useState<{
    pageNo: number;
    pageSize?: number;
  }>({
    pageNo: 1,
    pageSize: 5
  });

  // 抽屉相关
  const [visible, setVisible] = useState(false);
  const [versionInfo, setVersionInfo] = useState<any>({});

  // 详情字段
  const infoList = useMemo(() => {
    const info: any = dataInfo || {};
    return [
      {
        label: `${MetaCnNameMap['Catalog']}名称`,
        value: info.catalogName
      },
      {
        label: `${MetaCnNameMap['Schema']}名称`,
        value: info.schemaName
      },
      {
        label: `${MetaCnNameMap['Operator']}名称`,
        value: info.name
      },
      {
        label: `${MetaCnNameMap['Operator']}别名`,
        value: info.alias
      },
      {
        label: '使用说明',
        value: info.usage
      },
      {
        label: '创建时间',
        value: info.createdAt
      },
      {
        label: '创建人',
        value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
      },
      {
        label: '修改时间',
        value: info.updatedAt
      },
      {
        label: '最近修改人',
        value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
      },
      {
        label: '最新版本 ID',
        value: info.latestVersionId
      },
      {
        label: '最新版本号',
        value: info.latestVersionName
      }
    ];
  }, [dataInfo, userList]);

  const deleteVersion = useMemoizedFn((operateVersion) => {
    Modal.confirm({
      title: `删除版本`,
      content: `删除后⽆法恢复！请确定是否要删除 “${operateVersion.name}”`,
      okText: '删除',
      onOk() {
        return deleteOperatorVersion(workspaceId, fullName, operateVersion.name).then(() => {
          toast.success({
            message: '删除成功！',
            duration: 3
          });
          runVersionList();
        });
      }
    });
  });

  const authCheck = useMemoizedFn((privileges: string[], authName: string) => {
    return privileges?.includes(authName);
  });
  //
  const versionColumns = useMemo(
    () => [
      {
        title: '版本号',
        dataIndex: 'name',
        width: 100,
        render: (text: string, record: any) => (
          <Button
            className="version-name-btn"
            type="actiontext"
            onClick={(e) => {
              e.stopPropagation();
              setVisible(true);
              setVersionInfo(record);
            }}
          >
            {text}
          </Button>
        )
      },
      {
        title: 'ID',
        dataIndex: 'id',
        width: 150
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        width: 180
      },
      {
        title: '更新时间',
        dataIndex: 'updatedAt',
        width: 180,
        render: (text: string) => {
          return text || '-';
        }
      },
      {
        title: '版本描述',
        dataIndex: 'comment',
        render: (text: string) => {
          return <TextEllipsis text={text || '-'} height={20}></TextEllipsis>;
        }
      },
      {
        title: '操作',
        dataIndex: 'action',
        width: 100,
        render: (text: string, record: any) => (
          <Space>
            <AuthButton
              isAuth={authCheck(record.privileges, Privilege.CreateOperatorVersion)}
              className="version-name-btn"
              type="actiontext"
              onClick={(e) => {
                navigate(
                  `${urls.operatorVersionConfig}?workspaceId=${workspaceId}&fullName=${fullName}&version=${record.name}`
                );
              }}
            >
              修改
            </AuthButton>
            <AuthButton
              isAuth={authCheck(record.privileges, Privilege.CreateOperatorVersion)}
              className="version-name-btn"
              type="actiontext"
              onClick={(e) => {
                deleteVersion(record);
              }}
            >
              删除
            </AuthButton>
            {/* <AuthButton
              onClick={() =>
                permissionModalRef.current?.open({
                  resourceType: ResourceType.Operator,
                  resourceId: record.linkFile.tableId,
                  resourceName: record.name,
                  privilegeList: PRIVILEGE_LIST
                })
              }
              isAuth={authCheck(record.privileges, Privilege.CreateOperatorVersion)}
            >
              <span>权限管理</span>
            </AuthButton> */}
          </Space>
        )
      }
    ],
    []
  );

  // 获取详情
  const getOperatorDetail = useMemoizedFn(async () => {
    const res = await http.getOperatorDetail(workspaceId, fullName);
    setDataInfo(res.result);
  });

  const {
    data: versionData,
    loading,
    run: runVersionList
  } = useRequest(
    async () => {
      if (!catalog || !schema || !node) {
        return;
      }
      return http.getOperatorAllList(workspaceId, fullName, {
        pageNo: pagination.pageNo,
        pageSize: pagination.pageSize
      });
    },
    {
      refreshDeps: [pagination, catalog, schema, node] //  分页 和 排序变化时刷新
    }
  );

  // 初始化
  useEffect(() => {
    catalog && schema && node && getOperatorDetail();
  }, [catalog, schema, node]);

  // tab 切换
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  // 更新描述
  const onChangeDescript = useMemoizedFn(async (text: string, type = 'comment') => {
    await http.patchOperator(workspaceId, fullName, {[type]: text});
    getOperatorDetail();
  });

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  // 重命名
  const renameSuccessFun = useMemoizedFn(() => {
    changeUrlFunReplace((preState) => ({...preState, node: node}), true);
  });
  // 删除成功
  const removeSuccessFun = useMemoizedFn(() => {
    changeUrlFunReplace((preState) => ({...preState, node: '', type: ''}), true);
  });
  const onDropdownClick = useMemoizedFn((key) => {
    if (key === 'rename') {
      setRenameModal(true);
    } else if (key === 'remove') {
      ModalRemoveFun({
        fullName,
        name: node,
        title: 'Operator',
        requestFun: deleteOperator,
        successFun: removeSuccessFun,
        workspaceId: workspaceId
      });
    }
  });

  // 新建
  const onCreateClick = useCallback(() => {
    navigate(`${urls.operatorVersionConfig}?workspaceId=${workspaceId}&fullName=${fullName}`);
  }, []);

  const renderTab = useMemo(() => {
    const config = {
      [PanelEnum.DETAIL]: <InfoPanel infoList={infoList} title="基本信息" />,
      [PanelEnum.OVERVIEW]: (
        <div>
          <DescriptionEdit
            className="work-meta-operator-panel-edit"
            text={dataInfo?.comment || ''}
            onChangeText={(text) => onChangeDescript(text, 'comment')}
            hasEdit={catalog !== CatalogType.SYSTEM}
            authList={dataInfo?.privileges || []}
          />
          <div className="work-meta-operator-panel-usage">
            <DescriptionEdit
              title={
                <>
                  <IconSvg type="metadata-usage-doc" size={20} color="#6c6d70" />
                  使用说明
                </>
              }
              placeholder="请添加使用说明"
              className="work-meta-operator-panel-edit"
              text={dataInfo?.usage || ''}
              onChangeText={(text) => onChangeDescript(text, 'usage')}
              hasEdit={catalog !== CatalogType.SYSTEM}
              authList={dataInfo?.privileges || []}
            />
          </div>
          <div>
            <h2 className="title-head">版本列表</h2>
            <Table
              columns={versionColumns}
              dataSource={versionData?.result?.versions || []}
              loading={loading}
              pagination={false}
            />
            <div className="work-meta-operator-panel-pagination-container">
              <Pagination
                current={pagination.pageNo}
                total={versionData?.result?.totals || 0}
                pageSize={pagination.pageSize}
                onChange={(page, pageSize) => {
                  setPagination({
                    pageNo: page,
                    pageSize: pageSize
                  });
                }}
              />
            </div>
          </div>
        </div>
      ),
      [PanelEnum.PERMISSION]: (
        <PermissionManage
          resourceType={ResourceType.Operator}
          resourceId={fullName}
          hasInheritedFrom
          name={node}
          onSuccess={getOperatorDetail}
        />
      )
    };
    return config[tab];
  }, [
    dataInfo?.comment,
    dataInfo?.usage,
    fullName,
    infoList,
    loading,
    node,
    onChangeDescript,
    pagination.pageNo,
    pagination.pageSize,
    tab,
    versionColumns,
    versionData?.result?.totals,
    versionData?.result?.versions,
    getOperatorDetail
  ]);

  return (
    <div className="work-meta-operator-panel">
      {/* 标题导航操作栏 */}
      <MetaActionBar
        catalog={catalog}
        icon={<IconSvg type="meta-operator" size={20} color="#fff" />}
        title={node as string}
        dropdownMenu={dropdownMenu}
        onDropdownClick={onDropdownClick}
        authList={dataInfo?.privileges || []}
        createText={`创建${MetaCnNameMap['OperatorVersion']}`}
        createAuthName={Privilege.CreateOperatorVersion}
        onCreateClick={onCreateClick}
      />
      {/* Tabs */}
      <MetaTabs
        panesList={initialPanes}
        tab={tab}
        onTabChange={onTabChange}
        authList={dataInfo?.privileges || []}
      />
      {renderTab}
      <VersionDetailDrawer visible={visible} versionInfo={versionInfo} setVisible={setVisible} />
      {/** 重命名 Catalog 弹窗 */}
      <EditOperatorModal
        visible={showRenameModal}
        fullName={fullName}
        alias={dataInfo?.alias}
        workspaceId={workspaceId}
        handleCloseModal={() => setRenameModal(false)}
        successCallback={() => {
          renameSuccessFun();
        }}
      />
      <PermissionModal workspaceId={workspaceId} ref={permissionModalRef} />
    </div>
  );
};
export default PanelOperator;
