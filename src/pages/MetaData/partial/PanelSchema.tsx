import {useCallback, useContext, useEffect, useMemo, useState} from 'react';

import DescriptionEdit from '@components/DescriptionEdit';
import InfoPanel from '@components/InfoPanel';
import IconSvg from '@components/IconSvg';

import * as http from '@api/metaRequest';
import {EnumNodeType, IUrlStateHandler} from '../index';

import MetaActionBar from '../components/MetaActionBar';
import MetaTabs from '../components/MetaTabs';
import ModalRemoveFun from '../components/ModalRemoveFun';
import ModalRename from '../components/ModalRename';
import CreateDatasetAndModelModal from '../components/CreateDatasetAndModelModal';
import CreateVolumeModal from '@components/MetaCreateModal/CreateVolumeModal';
import {OutlinedPlusNew} from 'acud-icon';
import {MetaCnNameMap, ruleMapByCatalog} from '../config';
import {CatalogType} from '@api/metaRequest';
import {ResourceType, Privilege} from '@api/permission/type';
import PermissionManage from '../../../components/PermissionManage';
import {isBuildInCatalog} from '../helper';
import {Button, Loading} from 'acud';
import {edapIframeSrc} from '@components/IframePreloader';
import {WorkspaceContext} from '@pages/index';
import {DorisSchemaConfig, SchemaConfig} from '@components/PermissionManage/ constants';
import CreateOperatorModal from '../components/operator/CreateOperatorModal';

enum PanelEnum {
  DETAIL = '1',
  PERMISSION = '2'
}

const PanelSchema = (props: IUrlStateHandler) => {
  const {urlState, changeUrlFun, changeUrlFunReplace, handleTreeRefresh, userList, canWrite} = props;
  const {catalog = '', schema = '', tab = PanelEnum.DETAIL} = urlState;

  // 获取工作空间ID
  const {workspaceId} = useContext(WorkspaceContext);
  // schema 全名
  const fullName = `${catalog}.${schema}`;

  const [isCreateVolumeModalVisible, setIsCreateVolumeModalVisible] = useState(false);
  const [isCreateDatasetAndModelModalVisible, setIsCreateDatasetAndModelModalVisible] = useState(false);
  // 算子
  const [isCreateOperatorModalVisible, setIsCreateOperatorModalVisible] = useState(false);

  const [currentKey, setCurrentKey] = useState<string>('addDataset');
  // 详情
  const [dataInfo, setDataInfo] = useState<{
    schema?: http.ISchemaDetailRes;
    metadataSummary?: http.ISchemaSummaryRes;
  }>({});

  // catalog 类型
  const [catalogType, setCatalogType] = useState<CatalogType>(CatalogType.SYSTEM);

  const [loading, setLoading] = useState<boolean>(false);

  const [authList, setAuthList] = useState<Privilege[]>([]); // 权限点列表

  const initialPanes = useMemo(
    () => [
      {tab: '详情', key: PanelEnum.DETAIL},
      ...(isBuildInCatalog(catalog as CatalogType)
        ? []
        : [{tab: '权限管理', key: PanelEnum.PERMISSION, privilege: [Privilege.Manage]}])
    ],
    [catalog]
  );

  const dropdownMenu = useMemo(
    () => [
      {
        key: 'rename',
        label: `重命名${MetaCnNameMap['Schema']}`,
        disable: !canWrite,
        authName: Privilege.Manage
      },
      {
        key: 'remove',
        label: `删除${MetaCnNameMap['Schema']}`,
        disable: !canWrite,
        authName: Privilege.Manage
      }
    ],
    [canWrite]
  );

  // 立即新建按钮「下拉列」
  const createMenu = useMemo(() => {
    if (catalogType === CatalogType.DORIS) {
      return [];
    }
    return [
      // {key: 'addTable', label: '创建 Table'},
      {
        key: 'addVolume',
        label: `创建${MetaCnNameMap['Volume']}`,
        disable: !canWrite,
        authName: Privilege.CreateVolume
      },
      {
        key: 'addDataset',
        label: `创建${MetaCnNameMap['Dataset']}`,
        disable: !canWrite,
        authName: Privilege.CreateDataset
      },
      {
        key: 'addModel',
        label: `创建${MetaCnNameMap['Model']}`,
        disable: !canWrite,
        authName: Privilege.CreateModel
      },
      {
        key: 'addOperator',
        label: `创建${MetaCnNameMap['Operator']}`,
        disable: !canWrite,
        authName: Privilege.CreateModel
      }
    ];
  }, [canWrite, catalogType]);

  // 详情字段类型配置
  const schemaInfoFieldsMap = useMemo(
    () => ({
      [CatalogType.EDAP_DATALAKE]: [
        'catalogName',
        'schemaName',
        'storageLocation',
        'createdAt',
        'createdBy',
        'tableCount'
      ],
      [CatalogType.DORIS]: ['catalogName', 'schemaName', 'tableCount'],
      [CatalogType.DATALAKE]: [
        'catalogName',
        'schemaName',
        'storageLocation',
        'createdAt',
        'createdBy',
        'updatedAt',
        'updatedBy',
        'tableCount',
        'volumeCount',
        'operatorCount',
        'datasetCount',
        'modelCount'
      ]
      // 其他类型可按需补充
    }),
    []
  );

  const infoList = useMemo(() => {
    const info: any = dataInfo?.schema || {};
    // 获取要展示的字段 list，如找不到默认使用 DATALAKE 类型
    const fields = schemaInfoFieldsMap[catalogType] || schemaInfoFieldsMap[CatalogType.DATALAKE];
    return fields
      .map((key) => {
        switch (key) {
          case 'catalogName':
            return {
              label: `${MetaCnNameMap['Catalog']}名称`,
              value: info.catalogName
            };
          case 'schemaName':
            return {
              label: `${MetaCnNameMap['Schema']}名称`,
              value: info.name
            };
          case 'storageLocation':
            return {
              label: '存储路径',
              value: info.storageLocation
            };
          case 'createdAt':
            return {
              label: '创建时间',
              value: info.createdAt
            };
          case 'createdBy':
            return {
              label: '创建人',
              value: userList.find((item) => item.id === info.createdBy)?.name || info.createdBy
            };
          case 'updatedAt':
            return {
              label: '修改时间',
              value: info.updatedAt
            };
          case 'updatedBy':
            return {
              label: '最近修改人',
              value: userList.find((item) => item.id === info.updatedBy)?.name || info.updatedBy
            };
          case 'tableCount':
            return {
              label: `${MetaCnNameMap['Table']}个数`,
              value: `${dataInfo?.metadataSummary?.tableCount || 0}个`
            };
          case 'volumeCount':
            return {
              label: `${MetaCnNameMap['Volume']}个数`,
              value: `${dataInfo?.metadataSummary?.volumeCount || 0}个`
            };
          case 'operatorCount':
            return {
              label: `${MetaCnNameMap['Operator']}个数`,
              value: `${dataInfo?.metadataSummary?.operatorCount || 0}个`
            };
          case 'datasetCount':
            return {
              label: `${MetaCnNameMap['Dataset']}个数`,
              value: `${dataInfo?.metadataSummary?.datasetCount || 0}个`
            };
          case 'modelCount':
            return {
              label: `${MetaCnNameMap['Model']}个数`,
              value: `${dataInfo?.metadataSummary?.modelCount || 0}个`
            };
          default:
            return null;
        }
      })
      .filter(Boolean);
  }, [catalogType, dataInfo, userList, MetaCnNameMap, schemaInfoFieldsMap]);

  // 获取详情
  const getSchemaDetail = async () => {
    setLoading(true);
    try {
      // 获取 catalog 类型，根据类型获取详情展示形式，system 不需要请求
      if (catalog !== CatalogType.SYSTEM) {
        const catalogDetail = await http.getCatalogDetail(workspaceId, catalog);
        setCatalogType(catalogDetail?.result?.catalog?.type);
      }

      const res = await http.getSchemaDetail(workspaceId, fullName);
      setDataInfo(res.result);
      setAuthList(res.result?.schema?.privileges || []);
    } catch {
      console.error('获取 Schema 详情失败');
    } finally {
      setLoading(false);
    }
  };

  // 初始化
  useEffect(() => {
    catalog && schema && getSchemaDetail();
  }, [catalog, schema]);

  // 更新描述
  const onChangeDescript = useCallback(
    async (text: string) => {
      await http.patchSchema(workspaceId, fullName, {comment: text});
      getSchemaDetail();
    },
    [dataInfo, setDataInfo, catalog, schema]
  );

  // 重命名 & 删除
  const [showRenameModal, setRenameModal] = useState<boolean>(false);
  const renameSuccessFun = useCallback(
    (formData: {name: string}) => {
      changeUrlFunReplace((preState) => ({...preState, schema: formData.name}), true);
    },
    [changeUrlFunReplace]
  );
  const removeSuccessFun = useCallback(() => {
    changeUrlFunReplace(
      (preState) => ({
        ...preState,
        catalog: CatalogType.SYSTEM,
        schema: '',
        type: ''
      }),
      true
    );
  }, [changeUrlFunReplace]);
  const onDropdownClick = useCallback(
    (key) => {
      if (key === 'rename') {
        setRenameModal(true);
      } else if (key === 'remove') {
        ModalRemoveFun({
          fullName,
          name: schema,
          title: 'Schema',
          requestFun: http.deleteSchema,
          successFun: removeSuccessFun,
          workspaceId: workspaceId
        });
      }
    },
    [schema, removeSuccessFun]
  );

  // 新建 Table & Volume & Operator
  const onCreateClick = useCallback((key) => {
    if (key === 'addVolume') {
      setIsCreateVolumeModalVisible(true);
    }
    if (key === 'addDataset' || key === 'addModel') {
      setCurrentKey(key);
      setIsCreateDatasetAndModelModalVisible(true);
    }
    if (key === 'addOperator') {
      setIsCreateOperatorModalVisible(true);
    }
  }, []);

  // tab 切换 (暂只有一个)
  const onTabChange = useCallback(
    (tabkey) => {
      changeUrlFun((preState) => ({...preState, tab: tabkey}));
    },
    [changeUrlFun]
  );

  const renameNameRules = useMemo(() => {
    const ruleInfo = ruleMapByCatalog[catalogType];
    return [
      {
        validator: async (_, value) => {
          // 校验特殊字符和长度限制
          if (!ruleInfo.rule.test(value)) {
            return Promise.reject(new Error(ruleInfo.text));
          }
          // 异步校验Volume名称是否重复，复用查询接口 silent模式
          const res = await http.getSchemaDetail(workspaceId, `${catalog}.${value}`, true);
          if (res.success && res.result?.schema?.id) {
            return Promise.reject(new Error(`该${MetaCnNameMap['Schema']}名称已存在，请重新输入`));
          }
          return Promise.resolve();
        }
      }
    ];
  }, [catalog, catalogType, workspaceId]);

  return (
    <div className="work-meta-schema-panel">
      <Loading loading={loading}>
        {/* 标题导航操作栏 */}
        <MetaActionBar
          catalog={catalog}
          icon={<IconSvg type="meta-schema" size={20} color="#fff" />}
          title={schema}
          dropdownMenu={dropdownMenu}
          onDropdownClick={onDropdownClick}
          createText="立即创建"
          createIcon={<OutlinedPlusNew width={16} height={16} />}
          createMenu={createMenu}
          onCreateClick={onCreateClick}
          createDisabled={catalogType === CatalogType.DORIS}
          createDisabledTooltip="可视化建表暂未开放，仅支持 OpenAPI 建表"
          authList={authList}
          hiddenCreate={catalogType === CatalogType.EDAP_DATALAKE}
          otherButton={
            catalogType === CatalogType.EDAP_DATALAKE ? (
              <Button
                onClick={() =>
                  window.open(
                    `${edapIframeSrc}#/meta-data/manage?type=EDAP&topic=DataLake&database=${schema}`,
                    '_blank'
                  )
                }
              >
                去管理
              </Button>
            ) : null
          }
        />
        {/* Tabs */}
        <MetaTabs panesList={initialPanes} tab={tab} onTabChange={onTabChange} authList={authList} />
        {/* Tab-Panel 1 */}
        {tab === PanelEnum.DETAIL ? (
          <>
            {catalogType === CatalogType.DORIS ? null : (
              <DescriptionEdit
                text={dataInfo?.schema?.comment || ''}
                onChangeText={onChangeDescript}
                authList={authList}
                hasEdit={!isBuildInCatalog(catalog as CatalogType)} // 内置的 catalog 不允许编辑
              />
            )}
            <InfoPanel infoList={infoList} title="基本信息" />
          </>
        ) : null}
        {tab === PanelEnum.PERMISSION ? (
          <PermissionManage
            resourceType={ResourceType.Schema}
            resourceId={fullName}
            hasInheritedFrom
            name={schema}
            config={catalogType === CatalogType.DORIS ? DorisSchemaConfig : SchemaConfig}
            onSuccess={getSchemaDetail}
          />
        ) : null}
      </Loading>
      {/** 重命名 Schema 弹窗 */}
      <ModalRename
        visible={showRenameModal}
        setVisible={setRenameModal}
        fullName={fullName}
        nowValue={schema}
        title="Schema"
        requestFun={http.patchSchema}
        successFun={renameSuccessFun}
        nameRules={renameNameRules}
      />
      {/** 创建 Volume 弹窗 */}
      <CreateVolumeModal
        catalog={urlState.catalog}
        schema={urlState.schema}
        visible={isCreateVolumeModalVisible}
        handleCloseModal={() => setIsCreateVolumeModalVisible(false)}
        successCallback={(name) => {
          changeUrlFun((pre) => ({...pre, type: EnumNodeType.VOLUME, node: name}));
          handleTreeRefresh && handleTreeRefresh();
        }}
      />
      {/** 创建 Dataset 和 Model 弹窗 */}
      <CreateDatasetAndModelModal
        urlState={urlState}
        currentKey={currentKey}
        isModalVisible={isCreateDatasetAndModelModalVisible}
        handleCloseModal={() => setIsCreateDatasetAndModelModalVisible(false)}
        createdCallback={(name) => {
          changeUrlFun((pre) => ({
            ...pre,
            type: currentKey === 'addDataset' ? EnumNodeType.DATASET : EnumNodeType.MODEL,
            node: name
          }));
          handleTreeRefresh && handleTreeRefresh();
        }}
      />
      {/** 创建 Operator 弹窗 */}
      <CreateOperatorModal
        catalog={catalog}
        schema={schema}
        visible={isCreateOperatorModalVisible}
        handleCloseModal={() => setIsCreateOperatorModalVisible(false)}
        successCallback={(name) => {
          changeUrlFun((pre) => ({
            ...pre,
            type: EnumNodeType.OPERATOR,
            node: name
          }));
          handleTreeRefresh && handleTreeRefresh();
        }}
      />
    </div>
  );
};
export default PanelSchema;
