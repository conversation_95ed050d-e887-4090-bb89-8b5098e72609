import {createOperator} from '@api/metaRequest';
import {WorkspaceContext} from '@pages/index';
import {RULE} from '@utils/regs';
import {Form, Input, Modal, Select} from 'acud';
import {useMemoizedFn} from 'ahooks';
import {FC, useContext, useState} from 'react';
import styles from './index.module.less';
const {Option} = Select;

interface ICreateOperatorModalProps {
  catalog: string;
  schema: string;
  visible: boolean;
  handleCloseModal: () => void;
  successCallback: (name?: string) => void;
}

const CreateOperatorModal: FC<ICreateOperatorModalProps> = ({
  catalog,
  schema,
  visible,
  handleCloseModal,
  successCallback
}) => {
  const [loading, setLoading] = useState(false);
  const [form] = Form.useForm();

  const {workspaceId} = useContext(WorkspaceContext);
  const handleConfirm = useMemoizedFn(() => {
    form.validateFields().then(async (values) => {
      try {
        setLoading(true);
        const res = await createOperator(workspaceId, values);
        successCallback(res.result.name);
      } catch (error) {
        console.log(error);
      } finally {
        setLoading(false);
      }
    });
    handleCloseModal();
  });

  const onCloseModal = () => {
    handleCloseModal();
  };

  return (
    <Modal
      closable={true}
      title={'创建算子'}
      width={500}
      visible={visible}
      onOk={handleConfirm}
      onCancel={onCloseModal}
      okButtonProps={{loading}}
      destroyOnClose={true}
      className={styles['create-combine-modal']}
    >
      <Form labelAlign="left" layout="vertical" colon={false} labelWidth={80} form={form}>
        <Form.Item name="catalogName" initialValue={catalog} hidden></Form.Item>
        <Form.Item name="schemaName" initialValue={schema} hidden></Form.Item>
        <Form.Item
          label="算子名称"
          name="name"
          validateFirst
          rules={[
            {required: true, message: '请输入算子名称'},
            {
              validator: async (_, value) => {
                // 校验特殊字符和长度限制
                if (!RULE.specialNameStartEn64.test(value)) {
                  return Promise.reject(new Error(RULE.specialNameStartEn64Text));
                }
                return Promise.resolve();
              }
            }
          ]}
        >
          <Input
            placeholder="支持大小写字母、数字、下划线"
            allowClear
            limitLength={64}
            forbidIfLimit={true}
          />
        </Form.Item>

        <Form.Item
          label="算子别名"
          name="alias"
          validateFirst
          rules={[{required: true, message: '请输入算子名称'}]} // 校验特殊字符和长度限制
        >
          <Input
            placeholder="请输入算子别名，用于工作流中显示"
            allowClear
            limitLength={64}
            forbidIfLimit={true}
          />
        </Form.Item>

        <Form.Item label="描述" name="comment">
          <Input.TextArea
            placeholder="请输入算子描述"
            allowClear
            limitLength={150}
            autoSize={{minRows: 2, maxRows: 4}}
            forbidIfLimit
          />
        </Form.Item>

        <Form.Item label="使用说明" name="usage">
          <Input.TextArea
            placeholder="建议提案写算子详细介绍，如效果，算法介绍、建议配置信息等"
            allowClear
            limitLength={150}
            autoSize={{minRows: 2, maxRows: 4}}
            forbidIfLimit
          />
        </Form.Item>
      </Form>
    </Modal>
  );
};

export default CreateOperatorModal;
