import useUrlState from '@ahooksjs/use-url-state';
import {queryJobList} from '@api/job';
import {
  baseImages,
  createOperatorVersion,
  editOperatorVersion,
  operatorDetail,
  OperatorFieldTypeEnum,
  OperatorFieldTypeMap,
  OperatorResourceTypeEnum,
  OperatorResourceTypeMap,
  OperatorTypeEngineEnum,
  OperatorTypeEngineMap,
  OperatorTypeLanguageEnum,
  OperatorTypeLanguageMap
} from '@api/meta/operate';
import {getOperatorDetail, IOperatorDetailRes, IOperCategoryEnum} from '@api/metaRequest';
import IconSvg from '@components/IconSvg';
import LeaveFromModal from '@components/LeaveFromModal';
import RemoteSelect from '@components/RemoteSelect';
import TableEditList from '@components/TableEditList';
import TextEllipsis from '@components/TextEllipsisTooltip';
import {WorkspaceContext} from '@pages/index';
import urls from '@utils/urls';
import {Button, Checkbox, Form, Input, Radio, Select, Space, toast} from 'acud';
import {useMemoizedFn} from 'ahooks';
import React, {useContext, useEffect, useState} from 'react';
import {useNavigate} from 'react-router-dom';
import styles from './index.module.less';
import StorageLocation from './StorageLocation';
import {RULE} from '@utils/regs';

/**
 * 算子版本配置
 */
const ConfigOperatorVersion: React.FC = () => {
  // 离开时间
  const [leaveClickTime, setLeaveClickTime] = useState(0);
  const [isEditing, setIsEditing] = useState(false);

  const navigate = useNavigate();
  /** 是否是编辑任务 */
  const [{fullName, version}] = useUrlState();
  const [operatorVersion, setOperatorVersion] = useState<IOperatorDetailRes>();
  const [loading, setLoading] = useState(false);
  const [versionNum, setVersionNum] = useState(0);
  // 算子 名称 xx(xx) v1
  const [operatorName, setOperatorName] = useState('');

  const [form] = Form.useForm();

  const {workspaceId} = useContext(WorkspaceContext);

  // 保存
  const handleSubmit = useMemoizedFn(async () => {
    try {
      // 校验表单
      await form.validateFields();
    } catch {
      toast.error({message: '请检查表单是否正确'});

      return false;
    }

    setLoading(true);
    setIsEditing(false);

    const values = form.getFieldsValue();

    let res;
    if (version) {
      res = await editOperatorVersion(workspaceId, values);
    } else {
      res = await createOperatorVersion(workspaceId, values);
    }
    setLoading(false);

    if (res.success) {
      toast.success({message: '保存成功', duration: 5});
      goBack();
      return true;
    } else {
      setIsEditing(true);
      return false;
      // toast.error({message: '保存失败'});
    }
  });

  // 返回
  const goBack = useMemoizedFn(() => {
    const catalogName = form.getFieldValue('catalogName');
    const schemaName = form.getFieldValue('schemaName');
    const operatorName = form.getFieldValue('operatorName');
    const version = form.getFieldValue('version');

    navigate(
      `${urls.metaData}?catalog=${catalogName}&node=${operatorName}&refresh=true&schema=${schemaName}&type=operator&workspaceId=${workspaceId}`
    );
  });
  // 取消
  const handleCancel = useMemoizedFn(() => {
    goBack();
  });

  /** 初始化 */
  const initFn = useMemoizedFn(async () => {
    const res = await getOperatorDetail(workspaceId, fullName);
    const obj: any = res.result;
    setOperatorVersion(obj);

    if (version) {
      const resVersion = await operatorDetail(workspaceId, {fullName, versionName: version});

      setVersionNum(Number(version?.split('v')[1]));
      setOperatorName(`${obj.alias}(${obj.name}) ${version}`);
      form.setFieldsValue(resVersion.result);
    } else {
      const versionNum = Number(obj.latestVersionName?.split('v')[1] || 0) + 1;
      setVersionNum(versionNum);
      setOperatorName(`${obj.alias}(${obj.name}) v${versionNum}`);
    }
    const versionStr = version ? version : 'v' + versionNum;
    form.setFieldsValue({
      catalogName: obj.catalogName,
      schemaName: obj.schemaName,
      name: obj.name,
      operatorName: obj.name,
      version: versionStr
    });
  });

  useEffect(() => {
    initFn();
  }, [initFn]);

  const resourceType = Form.useWatch('resourceType', form);
  const engineType = Form.useWatch('engineType', form);

  const columnList = [
    {
      title: '字段名称',
      dataIndex: 'name',
      width: 200,
      rules: [{required: true, message: '请输入参数名称'}],
      renderComponent: <Input placeholder="请输入" />
    },
    {
      title: '字段类型',
      dataIndex: 'type',
      width: 100,
      rules: [{required: true, message: '请输入参数名称'}],
      renderComponent: (
        <Select
          options={Object.entries(OperatorFieldTypeMap).map(([key, value]) => ({
            label: value,
            value: key
          }))}
          placeholder="请选择类型"
        />
      )
    },
    {
      title: '非空',
      dataIndex: 'required',
      width: 100,
      valuePropName: 'checked',
      renderComponent: <Checkbox onChange={(e) => e.target.checked}></Checkbox>
    },
    {
      title: '字段描述',
      dataIndex: 'comment',
      width: 200,
      renderComponent: <Input placeholder="请输入"></Input>
    }
  ];
  const defaultValue = {name: '', type: OperatorFieldTypeEnum.STRING, required: false, comment: ''};
  return (
    <div className={styles['operator-version-config']}>
      <div className={styles['config-title']} onClick={handleCancel}>
        <IconSvg type="left" size={16} className="mr-[12px]" />
        {version ? (
          <TextEllipsis tooltip={'编辑' + operatorName}>{'编辑' + operatorName}</TextEllipsis>
        ) : (
          <TextEllipsis tooltip={'创建' + operatorName}>{'创建' + operatorName}</TextEllipsis>
        )}
      </div>
      <div className={styles['config-container']}>
        <div className={styles['config-form']}>
          <Form
            onValuesChange={() => setIsEditing(true)}
            form={form}
            labelWidth="80px"
            labelAlign="left"
            inputMaxWidth={900}
            colon={false}
            initialValues={{
              language: OperatorTypeLanguageEnum.PYTHON,
              engineType: [OperatorTypeEngineEnum.RAY],
              resourceType: OperatorResourceTypeEnum.CPU,
              category: IOperCategoryEnum.Source
            }}
          >
            <Form.Item name="name" label="Operator Name" noStyle hidden></Form.Item>
            <Form.Item name="catalogName" noStyle hidden></Form.Item>
            <Form.Item name="schemaName" noStyle hidden></Form.Item>
            <Form.Item name="operatorName" noStyle hidden></Form.Item>
            <Form.Item name="version" noStyle hidden></Form.Item>
            <Form.Item name="workspaceId" noStyle hidden></Form.Item>

            <div className={styles['form-item-title']}>基本信息</div>
            <Form.Item label="算子名称" name="name">
              {operatorVersion?.name}
            </Form.Item>
            <Form.Item label="算子别名" name="alias">
              {operatorVersion?.alias}
            </Form.Item>
            <Form.Item label="算子版本">v{versionNum}</Form.Item>
            <Form.Item label="版本描述" name="comment">
              <Input.TextArea
                placeholder="请输入描述"
                limitLength={150}
                forbidIfLimit
                autoSize={{minRows: 3, maxRows: 6}}
              />
            </Form.Item>
            <Form.Item name="category" label="算子类型">
              <Select
                options={Object.entries(IOperCategoryEnum).map(([key, value]) => ({
                  label: value,
                  value: value
                }))}
                placeholder="请选择"
              />
            </Form.Item>

            <div className={styles['form-item-title']}>代码配置</div>
            <Form.Item label="代码语言" name="language">
              <Select
                options={Object.entries(OperatorTypeLanguageMap).map(([key, value]) => ({
                  label: value,
                  value: key
                }))}
                placeholder="请选择"
              />
            </Form.Item>
            {/* 执行代码 上传 */}
            <StorageLocation form={form} />
            <div className={styles['form-item-title']}>运行约束</div>
            <Form.Item label="支持引擎" name="engineType">
              <Select
                mode="multiple"
                options={Object.entries(OperatorTypeEngineMap).map(([key, value]) => ({
                  label: value,
                  value: key
                }))}
                onSelect={() => {
                  form.setFieldsValue({
                    runtimeEnv: undefined
                  });
                }}
                placeholder="请选择"
              />
            </Form.Item>
            <Form.Item label="资源类型" name="resourceType">
              <Select
                options={Object.entries(OperatorResourceTypeMap).map(([key, value]) => ({
                  label: value,
                  value: key
                }))}
                onSelect={() => {
                  form.setFieldsValue({
                    runtimeEnv: undefined
                  });
                }}
                placeholder="请选择"
              />
            </Form.Item>

            <Form.Item
              label="类名"
              name={['properties', 'className']}
              rules={[
                {required: true, message: '请输入类名'},
                {
                  pattern: RULE.specialName64,
                  message: RULE.specialName64Text
                }
              ]}
            >
              <Input placeholder="请输入" />
            </Form.Item>

            <Form.Item label="执行镜像">
              <Radio.Group value="default">
                <Radio.Button value="default">官方镜像</Radio.Button>
                <Radio.Button value="xxx" disabled>
                  自定义镜像
                </Radio.Button>
              </Radio.Group>
            </Form.Item>
            <Form.Item label=" " name="runtimeEnv" rules={[{required: true, message: '请选择镜像'}]}>
              <RemoteSelect
                dropdownMatchSelectWidth={false}
                showTitle={true}
                dropdownStyle={{maxWidth: 300}}
                dropdownSearch
                maxTagCount={'responsive'}
                allowClear
                optionFilterProp="label"
                objName="imageName"
                objId="baseImageId"
                queryList={baseImages}
                params={[engineType?.join(','), resourceType]}
                placeholder="请选择镜像"
              />
            </Form.Item>

            <div className={styles['form-item-title']}>参数设置</div>

            <div className={styles['form-item-table']}>
              <div className={styles['form-item-table-title']}>输入参数</div>
              <TableEditList
                isReadOnly={false}
                name="input"
                form={form}
                columnList={columnList}
                defaultValue={defaultValue}
              />
            </div>

            <div className={styles['form-item-table']}>
              <div className={styles['form-item-table-title']}>输出参数</div>
              <TableEditList
                isReadOnly={false}
                name="output"
                form={form}
                columnList={columnList}
                defaultValue={defaultValue}
              />
            </div>
            <div className={styles['form-item-table']}>
              <div className={styles['form-item-table-title']}>运行参数</div>
              <TableEditList
                isReadOnly={false}
                name="execParams"
                form={form}
                columnList={columnList}
                defaultValue={defaultValue}
              />
            </div>
          </Form>
        </div>
      </div>
      <div className={styles['config-footer']}>
        <Space>
          <Button type="default" onClick={handleSubmit}>
            保存
          </Button>
          <Button type="default" onClick={handleCancel}>
            取消
          </Button>
        </Space>
      </div>
      <LeaveFromModal
        leaveClickTime={leaveClickTime}
        isEditing={isEditing}
        onSave={handleSubmit}
        onClickAndRefresh={() => goBack()}
        title="当前数据暂未保存，如果现在关闭，未保存的数据内容将丢失。请确认是否要继续关闭"
      ></LeaveFromModal>
    </div>
  );
};

export default ConfigOperatorVersion;
