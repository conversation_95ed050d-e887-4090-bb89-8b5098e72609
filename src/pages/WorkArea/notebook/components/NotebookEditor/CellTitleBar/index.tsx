import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {
  ICellSidebarProps,
  NotebookCommandIds,
  INotebookState
} from '@baidu/db-jupyter-react/lib/components/notebook';
import {Dropdown, Menu, Input, Button, Tooltip} from 'acud';
import {useState, useEffect, useRef} from 'react';
import {cellMetaKey} from '../../../config';
import {useNotebookSave} from '../../../hook';
import {useNotebookStore} from '@baidu/db-jupyter-react/lib/components/notebook';
import AuthComponents from '@components/AuthComponents/AuthComponents';

// 解决外层tooltip组件不生效问题
Dropdown.__ACUD_BUTTON = true;
const cx = classNames.bind(styles);

function LanguageSelector(props: {
  model: ICellSidebarProps['model'];
  commands: ICellSidebarProps['commands'];
  notebook: INotebookState;
  disabled?: boolean;
}) {
  const {model, commands, notebook, disabled} = props;
  const defaultLanguage = model.type === 'code' ? 'Python' : 'Markdown';
  const [language, setLanguage] = useState(
    (model.getMetadata(cellMetaKey) as any)?.language || defaultLanguage
  );

  // 语言选项
  const languageOptions = ['Python', 'SQL', 'Markdown'];

  // 更新语言
  const handleLanguageChange = (newLanguage: string) => {
    setLanguage(newLanguage);
    const metadata = (model.getMetadata(cellMetaKey) as any) || {};
    model.setMetadata(cellMetaKey, {
      ...metadata,
      language: newLanguage
    });

    // 根据语言类型切换cell类型
    if (newLanguage === 'Markdown' && model.type !== 'markdown') {
      commands.execute(NotebookCommandIds.changeCellTypeToMarkdown).catch((reason) => {
        console.error('Failed to change cell type to markdown.', reason);
      });
    } else if ((newLanguage === 'Python' || newLanguage === 'SQL') && model.type !== 'code') {
      commands.execute(NotebookCommandIds.changeCellTypeToCode).catch((reason) => {
        console.error('Failed to change cell type to code.', reason);
      });
    }
  };

  const showUnCompatibleTips = (lang) => {
    const engine = notebook?.customData?.compute?.engine;
    if (!engine || lang === 'Markdown') {
      return null;
    }
    const engineLanguageMap = {
      Ray: ['Python'],
      Doris: ['SQL'],
      Spark: ['Python', 'SQL']
    };
    const languages = engineLanguageMap[engine];
    if (!languages.includes(lang)) {
      return (
        <Tooltip title={`连接${engine}计算实例，无法执行${lang}单元格`}>
          <div className={cx('tips')}>!</div>
        </Tooltip>
      );
    }
    return null;
  };

  // 语言菜单
  const languageMenu = (
    <Menu>
      {languageOptions.map((lang) => (
        <Menu.Item key={lang} onClick={() => handleLanguageChange(lang)}>
          {lang}
        </Menu.Item>
      ))}
    </Menu>
  );

  return (
    <Dropdown disabled={disabled} overlay={languageMenu} placement="bottomRight">
      <div className={cx('language-selector')}>
        {showUnCompatibleTips(language)}
        {language}
      </div>
    </Dropdown>
  );
}

LanguageSelector.__ACUD_BUTTON = true;

function TitleEditor(props: {model: ICellSidebarProps['model']; readOnly?: boolean}) {
  const {model, readOnly} = props;
  const [isEditing, setIsEditing] = useState(false);
  const [title, setTitle] = useState('');
  const inputRef = useRef<HTMLInputElement>(null);

  // 从cell的metadata中读取title
  useEffect(() => {
    const metadata = model.getMetadata(cellMetaKey) as any;
    setTitle(metadata?.title || '');
  }, [model]);

  // 保存title到cell的metadata
  const saveTitle = (newTitle: string) => {
    const metadata = (model.getMetadata(cellMetaKey) as any) || {};
    model.setMetadata(cellMetaKey, {
      ...metadata,
      title: newTitle
    });
    setTitle(newTitle);
    setIsEditing(false);
  };

  // 处理title编辑完成
  const handleTitleBlur = () => {
    saveTitle(title);
  };

  // 处理按键事件
  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      saveTitle(title);
    } else if (e.key === 'Escape') {
      setIsEditing(false);
      // 恢复原来的title
      const metadata = model.getMetadata(cellMetaKey) as any;
      setTitle(metadata?.title || '');
    }
  };

  // 点击title进入编辑模式
  const handleTitleClick = () => {
    if (readOnly) {
      return;
    }
    setIsEditing(true);
    // 使用setTimeout确保在DOM更新后聚焦
    setTimeout(() => {
      inputRef.current?.focus();
      inputRef.current?.select();
    }, 0);
  };

  const defaultTitle = readOnly ? '标题' : '点击添加标题';

  return (
    <div className={cx('cell-title')}>
      {isEditing ? (
        <Input
          ref={inputRef}
          value={title}
          onChange={(e) => setTitle(e.target.value)}
          onBlur={handleTitleBlur}
          onKeyDown={handleKeyDown}
          placeholder="输入单元格标题"
          size="small"
        />
      ) : (
        <div
          className={cx('title-text', {readonly: readOnly})}
          title={title || defaultTitle}
          onClick={handleTitleClick}
        >
          {title || defaultTitle}
        </div>
      )}
    </div>
  );
}

export default function CellTitleBar(props: ICellSidebarProps) {
  const {commands, model, extraPayload, index} = props;
  const {save} = useNotebookSave(extraPayload.notebookId, extraPayload.workspaceId, false);
  const notebookStore = useNotebookStore();
  const notebook = notebookStore.selectNotebook(extraPayload.notebookId);
  const {canView, canExecute, canModify, canManage} = extraPayload.privilege;

  function onRun() {
    commands
      .execute(NotebookCommandIds.run)
      .catch((reason) => {
        console.error('Failed to run cell.', reason);
      })
      .finally(() => {
        // 只有执行权限不保存
        if (canExecute) {
          return;
        }
        save();
      });
  }

  function activeCell() {
    commands.execute(NotebookCommandIds.activeCell, {model}).catch((reason) => {
      console.error('Failed to select cell.', reason);
    });
  }

  function onClearOutput() {
    activeCell();
    commands.execute(NotebookCommandIds.clearOutputs).catch((reason) => {
      console.error('Failed to clear outputs.', reason);
    });
  }

  function onCopyCell() {
    activeCell();
    commands.execute(NotebookCommandIds.copyCell).catch((reason) => {
      console.error('Failed to copy cell.', reason);
    });
  }

  function onCutCell() {
    activeCell();
    commands.execute(NotebookCommandIds.cutCell).catch((reason) => {
      console.error('Failed to cut cell.', reason);
    });
  }

  function onPasteCell() {
    activeCell();
    commands.execute(NotebookCommandIds.pasteCell).catch((reason) => {
      console.error('Failed to paste cell.', reason);
    });
  }

  function onInsertAbove() {
    activeCell();
    commands.execute(NotebookCommandIds.insertAbove).catch((reason) => {
      console.error('Failed to insert above.', reason);
    });
  }

  function onInsertBelow() {
    activeCell();
    commands.execute(NotebookCommandIds.insertBelow).catch((reason) => {
      console.error('Failed to insert below.', reason);
    });
  }

  function onRunAbove() {
    activeCell();
    commands
      .execute(NotebookCommandIds.runAllAbove)
      .catch((reason) => {
        console.error('Failed to run cell.', reason);
      })
      .finally(() => {
        save();
      });
  }

  function onRunBelow() {
    activeCell();
    commands
      .execute(NotebookCommandIds.runAllBelow)
      .catch((reason) => {
        console.error('Failed to run cell.', reason);
      })
      .finally(() => {
        save();
      });
  }

  const menu = (
    <Menu>
      <Menu.Item onClick={onCopyCell}>复制单元格</Menu.Item>
      <Menu.Item onClick={onCutCell}>剪切单元格</Menu.Item>
      <Menu.Item onClick={onPasteCell}>粘贴单元格</Menu.Item>
      <Menu.Item onClick={onInsertAbove}>在上方添加一个单元格</Menu.Item>
      <Menu.Item onClick={onInsertBelow}>在下方添加一个单元格</Menu.Item>
      <Menu.Item onClick={onRunAbove} disabled={notebook?.connectStatus !== 'CONNECTED'}>
        执行上方单元格
      </Menu.Item>
      <Menu.Item onClick={onRunBelow} disabled={notebook?.connectStatus !== 'CONNECTED'}>
        执行当前及下方单元格
      </Menu.Item>
      {model.type === 'code' && <Menu.Item onClick={onClearOutput}>清空输出</Menu.Item>}
    </Menu>
  );

  return (
    <div className={cx('cell-title-bar')}>
      <AuthComponents isAuth={!canView}>
        <Button
          type="primary"
          className={cx('run')}
          onClick={onRun}
          disabled={model.type === 'code' && notebook?.connectStatus !== 'CONNECTED'}
        ></Button>
      </AuthComponents>
      <TitleEditor readOnly={canView || canExecute} model={model} />
      <div className={cx('cell-right-action')}>
        <AuthComponents isAuth={canModify || canManage}>
          <LanguageSelector model={model} commands={commands} notebook={notebook} />
        </AuthComponents>
        <AuthComponents isAuth={canModify || canManage}>
          <Dropdown overlay={menu} placement="bottomRight">
            <div className={cx('more')}></div>
          </Dropdown>
        </AuthComponents>
      </div>
    </div>
  );
}
