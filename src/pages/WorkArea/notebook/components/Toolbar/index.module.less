.toolbar {
  height: 40px;
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 0 16px;
  border-bottom: 1px solid #d9d9d9;

  .item-wrap {
    padding: 2px 4px;
    margin-right: 4px;
    display: flex;
    align-items: center;
    border-radius: 4px;
    cursor: pointer;
    color: #151b26;

    &:hover {
      background-color: rgba(#070c14, 0.06);
    }

    &.disabled {
      cursor: not-allowed;
      color: #b8babf;
    }
  }

  .item {
    margin-right: 4px;
    height: 16px;
    width: 16px;
    background-size: 16px 16px;
    background-repeat: no-repeat;
    background-position: center;

    &.save {
      background-image: url('~@assets/originSvg/notebook/save.svg?url');
    }

    &.clear-all-outputs {
      background-image: url('~@assets/originSvg/notebook/clearoutputs.svg?url');
    }

    &.export {
      background-image: url('~@assets/originSvg/notebook/export.svg?url');
    }

    &.delete-all-cells {
      background-image: url('~@assets/originSvg/notebook/delete.svg?url');
    }
  }

  .divider {
    width: 1px;
    height: 12px;
    background-color: #d4d6d9;
  }
}
