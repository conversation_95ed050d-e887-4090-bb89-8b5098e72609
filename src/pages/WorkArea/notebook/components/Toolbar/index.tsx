import React from 'react';
import classNames from 'classnames/bind';
import styles from './index.module.less';
import {useNotebookAction} from '../../hook';
import {useHotkeys} from '@hooks/useHotkeys';
import {NotebookHotkeyContext, createToolbarHotkeys} from '../../hotkeys';
import {NotebookPrivilege} from '../../../utils';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import SaveIcon from '@assets/originSvg/notebook/save.svg';
import ExportIcon from '@assets/originSvg/notebook/export.svg';
import DeleteAllCellsIcon from '@assets/originSvg/notebook/delete.svg';
import ClearAllOutputsIcon from '@assets/originSvg/notebook/clearoutputs.svg';

const cx = classNames.bind(styles);

interface ToolbarProps {
  notebookId: string;
  notebookName: string;
  notebookPrivilege: NotebookPrivilege;
}
export default function Toolbar({notebookId, notebookName, notebookPrivilege}: ToolbarProps) {
  const {canView, canExecute, canModify, canManage} = notebookPrivilege;
  const {save, clearAllOutputs, exportNotebook, toggleAllLineNumbers, deleteAllCells} =
    useNotebookAction(notebookId);

  // 使用快捷键系统
  useHotkeys(
    NotebookHotkeyContext.TOOLBAR,
    createToolbarHotkeys({
      save
    })
  );

  function onExport() {
    exportNotebook(notebookName);
  }

  function onToggleAllLineNumbers() {
    toggleAllLineNumbers();
  }

  const placement = 'bottom';

  const saveItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="save" onClick={save}>
        保存
      </ActionBtn>
    </AuthComponents>
  );

  const exportItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="export" onClick={onExport}>
        导出
      </ActionBtn>
    </AuthComponents>
  );

  const deleteAllCellsItem = (
    <AuthComponents isAuth={canModify || canManage}>
      <ActionBtn icon="delete-all-cells" onClick={deleteAllCells}>
        清空单元格
      </ActionBtn>
    </AuthComponents>
  );

  const clearAllOutputsItem = (
    <AuthComponents isAuth={canExecute || canModify || canManage}>
      <ActionBtn icon="clear-all-outputs" onClick={clearAllOutputs}>
        清空全部输出
      </ActionBtn>
    </AuthComponents>
  );

  const splitItem = <div className={cx('divider')}></div>;

  const items = [saveItem, exportItem, deleteAllCellsItem, splitItem, clearAllOutputsItem];
  return <div className={cx('toolbar')}>{items}</div>;
}

function ActionBtn(props: {icon: string; disabled?: boolean; onClick: (e) => void; children}) {
  const {icon, disabled, onClick, children} = props;
  const clickHandle = (e) => {
    if (disabled) return;
    onClick(e);
  };

  const IconMap = {
    save: SaveIcon,
    export: ExportIcon,
    'delete-all-cells': DeleteAllCellsIcon,
    'clear-all-outputs': ClearAllOutputsIcon
  };

  const Icon = IconMap[icon];
  return (
    <div className={cx('item-wrap', {disabled})} onClick={clickHandle}>
      <Icon className={cx('item')} />
      {children}
    </div>
  );
}

ActionBtn.displayName = 'ActionBtn';
// 解决外层tooltip组件不生效问题
ActionBtn.__ACUD_BUTTON = true;
