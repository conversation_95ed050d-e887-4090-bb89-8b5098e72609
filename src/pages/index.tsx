import {AppLayout} from '@/components/AppLayout';
import flags from '@/flags';
import urls, {WORKSPACE_ROUTER_PREFIX} from '@/utils/urls';
import useUrlState from '@ahooksjs/use-url-state';
import {verifySystemAdmin} from '@api/permission';
import {Privilege} from '@api/permission/type';
import {createNotebook, getWorkspaceFolderList} from '@api/WorkArea';
import {ServiceParam} from '@baidu/bce-react-toolkit';
import AuthComponents from '@components/AuthComponents/AuthComponents';
import AuthMenuItem from '@components/AuthComponents/AuthMenuItem';
import {TooltipType} from '@components/AuthComponents/constants';
import IconSvg from '@components/IconSvg';
import {ErrorBoundary as ErrorBoundaryComponent} from '@components/WithoutPermissionPage/ErrorBoundary';
import WorkspaceHeader from '@components/WorkspaceHeader';
import WorkspaceOutHeader from '@components/WorkspaceOutHeader/private-index';
import useAuth from '@hooks/useAuth';
import {useRegion} from '@hooks/useRegion';
import IframeEdapPageView from '@pages/Iframe';
import Metastore from '@pages/Metastore';
import WorkspaceDetail from '@pages/Workspace/WorkspaceDetail';
import WorkspaceList from '@pages/Workspace/WorkspaceList';
import store, {IAppState} from '@store/index';
import {MenuItem} from '@type/common';
import {getWorkspacePrivilege} from '@utils/auth';
import {recursiveMenus} from '@utils/utils';
import {Loading, Menu} from 'acud';
import {useDebounceFn, useMemoizedFn} from 'ahooks';
import React, {ComponentType, createContext, FC, Suspense, useEffect, useMemo, useRef, useState} from 'react';
import {ErrorBoundary} from 'react-error-boundary';
import {useSelector} from 'react-redux';
import {Outlet, useLocation, useNavigate} from 'react-router-dom';
import MetaData from './MetaData/index';
const {SubMenu} = Menu;

const Activation = React.lazy(() => import(/* webpackChunkName: "Activation" */ '@pages/Activation'));

/** 权限定义 */
export enum EAuth {
  /** 元数据页 - 读权限 - 包含read、readWrite */
  GlobalMetastoreRead = 'globalMetastoreRead'
}

const JobWorkflow = React.lazy(() => import(/* webpackChunkName: "JobWorkflow" */ '@pages/JobWorkflow'));
const JobDetailPage = React.lazy(
  () => import(/* webpackChunkName: "JobDetailPage" */ '@pages/JobWorkflow/Jobs/DetailPage')
);
const JobInstance = React.lazy(() => import(/* webpackChunkName: "JobInstance" */ '@pages/JobInstance'));
const JobInstanceResultPage = React.lazy(
  () => import(/* webpackChunkName: "JobInstanceResultPage" */ '@pages/JobInstance/ResultPage')
);

const TemplateDetail = React.lazy(
  () => import(/* webpackChunkName: "TemplateDetail" */ '@pages/JobWorkflow/Templates/Detail')
);

const ConnectionPage = React.lazy(
  () => import(/* webpackChunkName: "ConnectionPage" */ '@pages/MetaData/Connection')
);

const ConnectionDetail = React.lazy(
  () => import(/* webpackChunkName: "ConnectionDetail" */ '@pages/MetaData/Connection/ConnectionDetail')
);

// const MetaData = React.lazy(
//   () => import(/* webpackChunkName: "MetaData" */ '@/pages/MetaData')
// );

const WorkArea = React.lazy(() => import(/* webpackChunkName: "WorkArea" */ '@pages/WorkArea'));
const Notebook = React.lazy(() => import(/* webpackChunkName: "Notebook" */ '@pages/WorkArea/notebook'));

const Compute = React.lazy(() => import(/* webpackChunkName: "Compute" */ '@pages/Compute/List'));
const ComputeCreateTaskInstance = React.lazy(
  () => import(/* webpackChunkName: "ComputeCreateTaskInstance" */ '@pages/Compute/Create/TaskInstance')
);
const ComputeCreateBasedInstance = React.lazy(
  () => import(/* webpackChunkName: "ComputeCreateBasedInstance" */ '@pages/Compute/Create/BasedInstance')
);
const ComputeCreateAnalysisInstance = React.lazy(
  () =>
    import(/* webpackChunkName: "ComputeAnalysisInstanceCreate" */ '@pages/Compute/Create/AnalysisInstance')
);
const ComputeTaskInstanceDetail = React.lazy(
  () => import(/* webpackChunkName: "ComputeTaskInstanceDetail" */ '@pages/Compute/Detail/TaskInstance')
);
const ComputeCreatePool = React.lazy(
  () => import(/* webpackChunkName: "ComputeCreatePool" */ '@pages/Compute/Create/ResourcePool')
);
const ComputePoolDetail = React.lazy(
  () => import(/* webpackChunkName: "ComputePoolDetail" */ '@pages/Compute/Detail/ResourcePool')
);

const IntegrationPage = React.lazy(
  () => import(/* webpackChunkName: "integration" */ '@pages/DataIntegration')
);

const FileCollectCreate = React.lazy(
  () =>
    import(
      /* webpackChunkName: "file-collect-create" */ '@pages/DataIntegration/FileCollect/FileCollectCreate'
    )
);

const FileCollectDetail = React.lazy(
  () =>
    import(
      /* webpackChunkName: "file-collect-create" */ '@pages/DataIntegration/FileCollect/FileCollectDetail'
    )
);

const ComputeCreateConnAndIntegInstance = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ComputeConnAndIntegInstanceCreate" */ '@pages/Compute/Create/ConnAndIntegInstance'
    )
);

const OfflineCollectDetail = React.lazy(
  () =>
    import(
      /* webpackChunkName: "OfflineCollectDetail" */ '@pages/DataIntegration/SqlCollect/OfflineCollect/OfflineCollectDetail'
    )
);

const OfflineCollectConfig = React.lazy(
  () =>
    import(
      /* webpackChunkName: "OfflineCollectConfig" */ '@pages/DataIntegration/SqlCollect/OfflineCollect/OfflineCollectConfig'
    )
);

const OfflineCollectResult = React.lazy(
  () =>
    import(
      /* webpackChunkName: "OfflineCollectResult" */ '@pages/DataIntegration/SqlCollect/OfflineCollect/OfflineCollectResult'
    )
);
const ConfigOperatorVersion = React.lazy(
  () =>
    import(
      /* webpackChunkName: "ConfigOperatorVersion" */ '@pages/MetaData/components/operator/ConfigOperatorVersion'
    )
);

/** 主菜单定义 */
export const menus: MenuItem[] = [
  {
    menuName: flags.DatabuilderPrivateSwitch ? '多模态空间' : '工作空间',
    key: urls.manageWorkspace,
    isNavMenu: true,
    Component: WorkspaceList,
    isPageWrapperNotRequired: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workarea" size={16} color="#303540" />,
    privilege: Privilege.WorkspacesMenu
  },
  ...(flags.DatabuilderPrivateSwitch
    ? [
        {
          menuName: '结构化空间',
          key: urls.manageEDAPWorkspace,
          isNavMenu: true,
          Component: IframeEdapPageView,
          isPageWrapperNotRequired: false,
          isPageLayoutCustomized: true,
          icon: <IconSvg type="nav-workarea-edap" fill="none" size={16} color="#303540" />,
          privilege: Privilege.WorkspacesMenu
        }
      ]
    : []),
  {
    menuName: '工作空间详情',
    key: urls.manageWorkspaceDetail,
    isNavMenu: false,
    Component: WorkspaceDetail,
    isPageWrapperNotRequired: true,
    activeMenuKey: '/manage-workspace',
    isPageLayoutCustomized: true,
    privilege: Privilege.WorkspacesMenu
  },
  {
    menuName: '元数据',
    key: urls.metastore,
    isNavMenu: true,
    Component: Metastore,
    isPageWrapperNotRequired: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-metadata" size={16} color="#303540" />,
    privilege: Privilege.MetastoreMenu
  }
];

/** 空间菜单定义 */
export const workspaceMenus: MenuItem[] = [
  {
    menuName: '工作区',
    key: urls.workArea,
    isNavMenu: true,
    Component: WorkArea,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workarea" size={16} color="#303540" />,
    privilege: Privilege.WorkspaceMenu
  },
  {
    menuName: 'notebook',
    key: urls.notebook,
    activeMenuKey: urls.workArea,
    isNavMenu: false,
    Component: Notebook,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    privilege: Privilege.WorkspaceMenu
  },
  {
    menuName: '元数据',
    key: urls.metaData,
    isNavMenu: true,
    Component: MetaData,
    isPageWrapperNotRequired: true,
    isHeaderNav: false,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-metadata" size={16} color="#303540" />,
    privilege: Privilege.CatalogMenu
  },

  {
    menuName: '元数据-算子配置',
    key: urls.operatorVersionConfig,
    isNavMenu: false,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    Component: ConfigOperatorVersion
  },
  {
    menuName: '数据源',
    key: urls.connection,
    isNavMenu: false,
    isPageLayoutCustomized: true,
    activeMenuKey: urls.metaData,
    Component: ConnectionPage,
    allowDirectOpen: true,
    privilege: Privilege.CatalogMenu
  },
  {
    menuName: '数据源详情',
    key: urls.connectionDetail,
    isNavMenu: false,
    isPageLayoutCustomized: true,
    activeMenuKey: urls.metaData,
    Component: ConnectionDetail,
    privilege: Privilege.CatalogMenu
  },
  {
    menuName: '计算实例',
    key: urls.compute,
    isNavMenu: true,
    Component: Compute,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-instance" size={16} color="#303540" />,
    privilege: Privilege.ComputeMenu
  },
  {
    menuName: '常驻实例创建',
    key: urls.computeCreateBasedInstance,
    isNavMenu: false,
    Component: ComputeCreateBasedInstance,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '分析实例创建',
    key: urls.computeCreateAnalysisInstance,
    isNavMenu: false,
    Component: ComputeCreateAnalysisInstance,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '任务实例创建',
    key: urls.computeCreateTaskInstance,
    isNavMenu: false,
    Component: ComputeCreateTaskInstance,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    disabledWorkspaceSelect: true
  },
  {
    menuName: '任务实例详情页',
    key: urls.computeTaskInstanceDetail,
    activeMenuKey: urls.compute,
    isNavMenu: false,
    Component: ComputeTaskInstanceDetail,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '资源池创建',
    key: urls.computeCreatePool,
    isNavMenu: false,
    Component: ComputeCreatePool,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    disabledWorkspaceSelect: true
  },
  {
    menuName: '资源池详情',
    key: urls.computePoolDetail,
    activeMenuKey: urls.compute,
    isNavMenu: false,
    Component: ComputePoolDetail,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    privilege: Privilege.ComputeMenu
  },
  {
    menuName: '源连接与集成实例创建',
    key: urls.computeCreateConnAndIntegInstance,
    isNavMenu: false,
    Component: ComputeCreateConnAndIntegInstance,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    privilege: Privilege.ComputeMenu
  },
  {
    menuName: '工作流',
    key: urls.job,
    isNavMenu: true,
    Component: JobWorkflow,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    icon: <IconSvg type="nav-workflow" size={16} color="#303540" />,
    privilege: Privilege.WorkflowMenu
  },
  {
    menuName: '工作流详情页',
    key: urls.jobDetail,
    isNavMenu: false,
    isPageLayoutCustomized: true,
    activeMenuKey: urls.job,
    Component: JobDetailPage,
    privilege: Privilege.WorkflowMenu
  },
  {
    menuName: '模板',
    key: urls.templateDetail,
    isNavMenu: false,
    activeMenuKey: urls.job,
    Component: TemplateDetail,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    privilege: Privilege.WorkflowMenu
  },
  {
    menuName: '运行记录结果',
    key: urls.jobResult,
    isNavMenu: false,
    activeMenuKey: urls.job,
    isPageLayoutCustomized: true,
    Component: JobInstanceResultPage,
    privilege: Privilege.WorkflowMenu
  },

  {
    menuName: '运行记录结果,需在运行记录结果页面中展示s',
    key: urls.jobInstanceResult,
    isNavMenu: false,
    activeMenuKey: urls.jobInstance,
    isPageLayoutCustomized: true,
    Component: JobInstanceResultPage,
    privilege: Privilege.WorkflowMenu
  },
  {
    menuName: '数据集成',
    key: urls.integration,
    isNavMenu: true,
    isPageLayoutCustomized: true,
    Component: IntegrationPage,
    icon: <IconSvg type="integration" size={15} fill="transparent" />,
    privilege: Privilege.IntegrationMenu
  },
  {
    menuName: '集成-文件采集详情',
    key: urls.fileCollectDetail,
    isNavMenu: false,
    activeMenuKey: urls.integration,
    isPageLayoutCustomized: true,
    Component: FileCollectDetail,
    privilege: Privilege.IntegrationMenu
  },
  {
    menuName: '集成-文件采集创建',
    key: urls.fileCollectCreate,
    isNavMenu: false,
    Component: FileCollectCreate,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true
  },
  {
    menuName: '集成-结构化-详情',
    key: urls.offlineCollectDetail,
    isNavMenu: false,
    activeMenuKey: urls.integration,
    isPageLayoutCustomized: true,
    Component: OfflineCollectDetail
  },
  {
    menuName: '集成-结构化-配置',
    key: urls.offlineCollectConfig,
    isNavMenu: false,
    isPageWrapperNotRequired: true,
    isPageLayoutCustomized: true,
    Component: OfflineCollectConfig
  },
  {
    menuName: '集成-结构化-结果',
    key: urls.offlineCollectResult,
    isNavMenu: false,
    activeMenuKey: urls.integration,
    isPageLayoutCustomized: true,
    Component: OfflineCollectResult,
    privilege: Privilege.IntegrationMenu
  },
  {
    menuName: '运行记录',
    key: urls.jobInstance,
    isNavMenu: true,
    isPageLayoutCustomized: true,
    Component: JobInstance,
    icon: <IconSvg type="nav-operate" size={15} color="#303540" />,
    privilege: Privilege.WorkflowInstanceMenu
  }
];

export const WorkspaceMenuPrivileges = workspaceMenus.reduce(
  (pre, cur) => (pre.includes(cur.privilege) ? pre : [...pre, cur.privilege]),
  []
);

/** 打平之后的主菜单列表 */
export const flattenedMenuList = recursiveMenus(menus);

/** 打平之后的空间菜单列表 */
export const workspaceFlattenedMenuList = recursiveMenus(workspaceMenus);

export interface IWorkspaceContextType {
  workspaceId: string;
}

export const WorkspaceContext = createContext<IWorkspaceContextType>({
  workspaceId: ''
});

const isPrivate = flags.DatabuilderPrivateSwitch;

const Main: FC<{
  menus: MenuItem[];
  component?: ComponentType<any>;
}> = ({menus, component: Component}) => {
  const [serviceParams, setServiceParams] = useState<ServiceParam[]>([]);
  const {currentRegion} = useRegion();
  // 融合态 - 成都region POC阶段集群线下创建集群，禁用计算实例相关创建按钮
  const isChengdu = !isPrivate && currentRegion.id === 'cd';

  const globalPermission = useSelector((state: IAppState) => state.globalAuthSlice.globalPermission);
  const workspacePermission = useSelector((state: IAppState) => state.globalAuthSlice.workspacePermission);

  // 使用replace 防止破坏history
  const [{workspaceId: urlWorkspaceId}, setUrlParams] = useUrlState(undefined, {
    navigateMode: 'replace'
  });
  const [curWorkspaceId, setCurWorkspaceId] = useState<string>(urlWorkspaceId);
  // 记录当前workspaceId  判断是初始化 还是 workspaceId 变化
  const curWorkspaceIdRef = useRef<string>(urlWorkspaceId);
  const [permLoading, setPermLoading] = useState(false);
  const location = useLocation();
  const navigate = useNavigate();
  const curMenu = useMemo(
    () => recursiveMenus(menus).find((item) => location.pathname === item.key),
    [location.pathname, menus]
  );
  // 禁用空间选择（任务实例创建页面）
  const isWorkspaceDisabled = curMenu?.disabledWorkspaceSelect ?? false;

  // 当前路径是否包含workspace 判断当前是否在workspace空间
  const pathnameHasWorkspace = location.pathname.includes(WORKSPACE_ROUTER_PREFIX);

  const handleWorkspacePerm = async (workspaceId: string, isChange: boolean) => {
    setPermLoading(true);
    const curMenu = recursiveMenus(menus).find((item) => location.pathname === item.key);

    if (!curMenu) {
      // TODO：处理当前页无权限
    }
    // 如果不是menu页签的首页   并且 有变化
    else if (curMenu && !curMenu.isNavMenu && curMenu.activeMenuKey && isChange) {
      navigate(curMenu.activeMenuKey, {replace: true});
    }
    setPermLoading(false);
  };

  // 项目权限请求
  useEffect(() => {
    if (curWorkspaceId) {
      // 需要判断是初始化 还是 有变化
      const isChange = curWorkspaceId !== curWorkspaceIdRef.current;
      // 更新当前workspaceId
      curWorkspaceIdRef.current = curWorkspaceId;
      handleWorkspacePerm(curWorkspaceId, isChange);
    }
  }, [curWorkspaceId]);

  const urlChangeFunc = useMemoizedFn(() => {
    // workspace 隐藏头部
    // 退出 workspace 后，显示头部
    const header = document.getElementById('header');
    if (pathnameHasWorkspace && header) {
      header.style.display = 'none';
    } else if (!pathnameHasWorkspace && header) {
      header.style.display = 'flex';
    }

    if (!curWorkspaceId && pathnameHasWorkspace && !urlWorkspaceId) {
      // TODO: 后面根据实际情况 定义到空间列表页面或者概览页面
      navigate(urls.manageWorkspace);
    }

    // 哈希变化 进入 workspace 更新当前 workspaceId
    if (urlWorkspaceId && pathnameHasWorkspace && urlWorkspaceId !== curWorkspaceId) {
      setCurWorkspaceId(urlWorkspaceId);
    }
    // 哈希变化后，如果还在workSpace 同步 workspaceId到url
    else if (!urlWorkspaceId && curWorkspaceId && pathnameHasWorkspace) {
      setUrlParams({workspaceId: curWorkspaceId});
    }
    // 退出 workspace 后，清除 workspaceId
    else if (!pathnameHasWorkspace && curWorkspaceId) {
      setCurWorkspaceId('');
    }
  });

  // 监听url变化
  useEffect(() => {
    urlChangeFunc();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [pathnameHasWorkspace, urlWorkspaceId]);

  // workspaceContextValue 防止不必要的 re-render
  const workspaceContextValue = useMemo(() => ({workspaceId: curWorkspaceId}), [curWorkspaceId]);

  const {run: handleCreateNotebook} = useDebounceFn(
    async () => {
      const folderres = await getWorkspaceFolderList({
        workspaceId: curWorkspaceId,
        parentId: ''
      });
      const home = folderres.result.find((item) => item.type === 'HOME');
      if (!home) {
        return;
      }

      const res = await createNotebook({
        workspaceId: curWorkspaceId,
        parentId: home.id
      });
      if (res.success) {
        navigate(
          `${urls.notebook}?workspaceId=${curWorkspaceId}&folderId=${home.id}&notebookId=${res.result.id}`
        );
      }
    },
    {wait: 500}
  );

  const QuickCreateConfig = useMemo(
    () => [
      {
        title: '文件离线采集',
        url: `${urls.fileCollectCreate}?`,
        icon: <IconSvg type="file-collect" size={16} fill="transparent" />,
        privilege:
          workspacePermission?.[Privilege.IntegrationMenu] &&
          workspacePermission?.[Privilege.UnstructuredIntegrationCreate]
      },
      ...(isPrivate
        ? []
        : [
            {
              title: '库表离线采集',
              url: `${urls.offlineCollectConfig}?sourceType=MySQL&`,
              icon: <IconSvg type="database-collect" size={16} fill="transparent" />,
              privilege:
                workspacePermission?.[Privilege.IntegrationMenu] &&
                workspacePermission?.[Privilege.StructuredIntegrationCreate]
            }
          ]),
      {
        title: 'Notebook',
        icon: <IconSvg type="nb-notebook" size={16} color="#303540" fill="none" />,
        handler: handleCreateNotebook,
        privilege: workspacePermission?.[Privilege.WorkspaceMenu]
      },
      {
        title: '工作流',
        url: `${urls.job}?create=true&`,
        icon: <IconSvg type="nav-workflow" size={16} color="#303540" />,
        privilege:
          workspacePermission?.[Privilege.WorkflowCreate] && workspacePermission?.[Privilege.WorkflowMenu]
      },
      {
        title: '计算实例',
        icon: <IconSvg type="nav-instance" size={16} color="#303540" />,
        privilege: workspacePermission?.[Privilege.ComputeMenu],
        children: [
          {
            title: '源连接与集成实例',
            url: `${urls.computeCreateConnAndIntegInstance}?`,
            privilege: workspacePermission?.[Privilege.IntegrationComputeCreate],
            disabled: isChengdu
          },
          {
            title: '数据处理 - 常驻实例',
            url: `${urls.computeCreateBasedInstance}?`,
            privilege: workspacePermission?.[Privilege.EtlComputeCreate],
            disabled: isChengdu
          },
          {
            title: '数据处理 - 任务实例模版',
            url: `${urls.computeCreateTaskInstance}?`,
            privilege: workspacePermission?.[Privilege.EtlJobTemplateCreate]
          },
          {
            title: '分析与AI搜索实例',
            url: `${urls.computeCreateAnalysisInstance}?`,
            privilege: workspacePermission?.[Privilege.AnalysisComputeCreate],
            disabled: isChengdu
          },
          {
            title: '资源池',
            url: `${urls.computeCreatePool}?`,
            privilege: workspacePermission?.[Privilege.ResourcePoolCreate],
            disabled: isChengdu
          }
        ]
      }
    ],
    [handleCreateNotebook, isChengdu, workspacePermission]
  );

  // 新增
  const menu = useMemo(() => {
    return (
      <Menu>
        {QuickCreateConfig.map((item) => {
          if (item?.children?.length) {
            const childrenMenu = item.children.map((item) => (
              <AuthMenuItem
                isAuth={item.privilege && !item.disabled}
                key={item.title}
                onClick={() => navigate(`${item.url}workspaceId=${curWorkspaceId}`)}
              >
                <div className="flex items-center">{item.title}</div>
              </AuthMenuItem>
            ));

            const title = (
              <div className="flex items-center">
                <span className="mr-[8px]">{item.icon}</span>
                {item.title}
              </div>
            );

            return (
              <AuthComponents key={item.title} isAuth={item.privilege} tooltipType={TooltipType.Function}>
                <SubMenu key={item.title} title={title}>
                  {childrenMenu}
                </SubMenu>
              </AuthComponents>
            );
          }
          return (
            <AuthMenuItem
              isAuth={item.privilege}
              key={item.title}
              onClick={item?.handler || (() => navigate(`${item.url}workspaceId=${curWorkspaceId}`))}
            >
              <div className="flex items-center">
                <span className="mr-[8px]">{item.icon}</span>
                {item.title}
              </div>
            </AuthMenuItem>
          );
        })}
      </Menu>
    );
  }, [QuickCreateConfig, curWorkspaceId, navigate]);

  // 若有auth字段，则根据权限过滤菜单
  const filteredMenus = useMemo(() => {
    return menus.filter(
      (route) => ({...workspacePermission, ...globalPermission})?.[route.privilege] || false
    );
  }, [globalPermission, menus, workspacePermission]);

  useEffect(() => {
    getWorkspacePrivilege(curWorkspaceId).then((res) => {
      store.dispatch({
        type: 'globalAuth/updateWorkspacePermission',
        payload: res
      });
    });
  }, [curWorkspaceId]);

  useEffect(() => {
    verifySystemAdmin().then((res) => {
      store.dispatch({
        type: 'globalAuth/updateSystemAdmin',
        payload: res.result
      });
    });
  }, []);

  return (
    <>
      {flags.DatabuilderPrivateSwitch ? (
        <WorkspaceOutHeader workspaceId={curWorkspaceId} />
      ) : curWorkspaceId ? (
        <WorkspaceHeader workspaceId={curWorkspaceId} isDisabled={isWorkspaceDisabled} />
      ) : null}
      <AppLayout
        menus={filteredMenus}
        serviceParams={serviceParams}
        {...(pathnameHasWorkspace
          ? {
              sidebarExtra: menu,
              hasMenuHead: false
            }
          : {})}
      >
        {Component && (
          <ErrorBoundary FallbackComponent={ErrorBoundaryComponent}>
            <Suspense fallback={<div>Loading...</div>}>
              <Component />
            </Suspense>
          </ErrorBoundary>
        )}
        {curWorkspaceId && permLoading && <Loading loading />}
        {curWorkspaceId && !permLoading && (
          <WorkspaceContext.Provider value={workspaceContextValue}>
            <ErrorBoundary FallbackComponent={ErrorBoundaryComponent}>
              <Outlet />
            </ErrorBoundary>
          </WorkspaceContext.Provider>
        )}
      </AppLayout>
    </>
  );
};

export default Main;
