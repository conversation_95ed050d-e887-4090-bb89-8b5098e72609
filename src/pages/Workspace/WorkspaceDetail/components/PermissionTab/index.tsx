/**
 * @file 工作空间详情 - 权限tab页
 * <AUTHOR>
 */

import {FC, useCallback, useEffect, useMemo, useRef, useState} from 'react';
import {Plus1} from '@baidu/xicon-react-bigdata';
import RefreshButton from '@components/RefreshButton';
import {Button, Pagination, Search, Table, toast, Modal, Loading, Space, Tag, Tooltip} from 'acud';
import {ColumnsType} from 'acud/lib/table';
import {Ellipsis} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import CreateModal from './components/CreateModal';
import {IQueryWorkspaceUserListParams, IWorkspace} from '@api/workspace';
import {formatTime} from '@utils/moment';
import {WorkspaceUserTypeMap} from '../../../constants';
import {OrderType} from '@utils/enums';
import useAuth from '@hooks/useAuth';

import styles from './index.module.less';
import {getWorkspaceUserList as queryWorkspaceUserList, updateWorkspaceUser} from '@api/permission';
import {PrincipalType, WorkspaceUser} from '@api/permission/type';
import {Privilege} from '@api/permission/type';
import {useSelector} from 'react-redux';
import {IAppState} from '@store/index';
import AuthButton from '@components/AuthComponents/AuthButton';

interface IWorkspacePermissionTabProps {
  workspaceDetail?: IWorkspace;
  privilege: Privilege[];
  getWorkspaceDetail: () => void;
}

// 工作空间 - 权限
export interface IWorkspaceUser {
  entityId: string;
  entityName: string;
  type: PrincipalType;
  role: string;
  createdAt: string;
}

const WorkspacePermissionTab: FC<IWorkspacePermissionTabProps> = ({
  workspaceDetail,
  privilege,
  getWorkspaceDetail
}) => {
  const [workspaceUserList, setWorkspaceUserList] = useState<Array<IWorkspaceUser>>([]);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [keyword, setKeyword] = useState('');
  const [typeFilterValue, setStatusFilterValue] = useState();
  const [createTimeSortValue, setCreateTimeSortValue] = useState<OrderType>();
  const [currentUser, setCurrentUser] = useState<IWorkspaceUser>();
  const [pagination, setPagination] = useState({
    pageNo: 1,
    pageSize: 10,
    total: 0
  });

  const isModify = useMemo(
    () => [Privilege.Modify, Privilege.FullControl].some((item) => privilege.includes(item)),
    [privilege]
  );

  // 点击新建按钮
  const onClickCreateBtn = useCallback(() => {
    setIsModalVisible(true);
  }, []);

  const {run: runDeleteWorkspaceUser} = useRequest(updateWorkspaceUser, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        toast.success({message: '删除成功', duration: 5});
        getWorkspaceUserList();
        getWorkspaceDetail();
      }
    }
  });

  const {run: runWorkspaceUserList, loading: queryWorkspaceUserListLoading} = useRequest(
    queryWorkspaceUserList,
    {
      manual: true,
      onSuccess: (res) => {
        if (res.success) {
          setWorkspaceUserList(
            res.result.principalInfos.map((item) => ({
              entityId: item.principal.id,
              entityName: item.principal.name,
              type: item.principal.type,
              role: item.roles[0].name,
              createdAt: item.createdAt
            }))
          );
          setPagination((prev) => ({...prev, total: res?.result?.totalCount || 0}));
        }
      }
    }
  );

  // 获取工作空间列表
  const getWorkspaceUserList = useCallback(() => {
    const params = {
      workspaceId: workspaceDetail?.id,
      pageNo: pagination.pageNo,
      pageSize: pagination.pageSize,
      principalName: keyword,
      principalType: typeFilterValue,
      order: createTimeSortValue
    };
    runWorkspaceUserList(params);
  }, [
    createTimeSortValue,
    keyword,
    pagination.pageNo,
    pagination.pageSize,
    runWorkspaceUserList,
    typeFilterValue,
    workspaceDetail?.id
  ]);

  // 初始化查询
  useEffect(() => {
    getWorkspaceUserList();
  }, [getWorkspaceUserList]);

  // 关闭弹窗
  const handleCloseModal = useCallback(() => {
    setCurrentUser(undefined);
    setIsModalVisible(false);
  }, []);

  // 数据总数
  const showTotal = useCallback(() => {
    return `共${pagination.total}条`;
  }, [pagination.total]);

  // 监听点击刷新按钮
  const onClickRefreshBtn = useCallback(() => {
    getWorkspaceUserList();
  }, [getWorkspaceUserList]);

  // 搜索
  const onConfirmSearch = useCallback((value: string) => {
    setKeyword(value);
    setPagination((prev) => ({...prev, pageNo: 1}));
  }, []);

  // 监听表格发生变化
  const onTableChange = useCallback((...args: any) => {
    // 筛选
    const type = args?.[1]?.type?.[0] || null;
    setStatusFilterValue(type);
    // 排序
    let createTimeSortValue = args?.[2]?.order;
    createTimeSortValue =
      createTimeSortValue === 'ascend'
        ? OrderType.asc
        : createTimeSortValue === 'descend'
          ? OrderType.desc
          : null;
    setCreateTimeSortValue(createTimeSortValue);
    setPagination((prev) => ({...prev, pageNo: 1}));
    setCreateTimeSortValue(createTimeSortValue);
  }, []);
  const columns: ColumnsType<IWorkspaceUser> = useMemo(() => {
    return [
      {
        title: '名称',
        dataIndex: 'entityName',
        key: 'entityName',
        width: '20%',
        ellipsis: {showTitle: false},
        render: (entityName) => {
          return <Ellipsis tooltip={entityName}>{entityName || '-'}</Ellipsis>;
        }
      },
      {
        title: '用户类型',
        dataIndex: 'type',
        key: 'type',
        width: '20%',
        filterMultiple: false,
        filters: Object.keys(WorkspaceUserTypeMap).map((key) => ({
          value: key,
          text: WorkspaceUserTypeMap[key]
        })),
        render: (status) => {
          return <span>{WorkspaceUserTypeMap[status] || '-'}</span>;
        }
      },
      {
        title: '角色',
        dataIndex: 'role',
        key: 'role',
        width: '20%'
      },
      {
        title: '创建时间',
        dataIndex: 'createdAt',
        key: 'createdAt',
        width: '20%',
        sorter: true,
        render: (time) => formatTime(time)
      },
      {
        title: '操作',
        width: '20%',
        fixed: 'right',
        render: (record: IWorkspaceUser) => {
          return (
            <Space align="center" style={{gap: 16, height: 20}} className={styles['table-operations']}>
              <AuthButton
                isAuth={isModify}
                type="actiontext"
                onClick={() => {
                  setCurrentUser(record);
                  setIsModalVisible(true);
                }}
              >
                编辑
              </AuthButton>
              <AuthButton
                isAuth={isModify}
                type="actiontext"
                onClick={() => {
                  Modal.confirm({
                    title: '删除成员',
                    content: `删除后无法恢复！请确定是否要删除成员“${record.entityName}”`,
                    onOk() {
                      const {entityId, type, role, entityName} = record;
                      runDeleteWorkspaceUser(workspaceDetail?.id, [
                        {
                          principal: {
                            id: entityId,
                            type,
                            name: entityName
                          },
                          removeRoleNames: [role]
                        }
                      ]);
                    },
                    onCancel() {}
                  });
                }}
              >
                删除
              </AuthButton>
            </Space>
          );
        }
      }
    ];
  }, [isModify, runDeleteWorkspaceUser, workspaceDetail?.id]);

  return (
    <div className={styles['workspace-permission-tab']}>
      <Loading loading={queryWorkspaceUserListLoading} />
      <div className={styles['operation-container']}>
        <div className={styles['left-container']}>
          <Search
            placeholder="请输入名称进行搜索"
            className={styles['search-container']}
            allowClear
            onSearch={onConfirmSearch}
          />
          <div className={styles['total-count']}>{showTotal()}</div>
        </div>
        <div className={styles['right-container']}>
          <RefreshButton onClick={onClickRefreshBtn}></RefreshButton>
          <AuthButton
            isAuth={isModify}
            icon={<Plus1 theme="line" size={16} strokeLinejoin="round" />}
            onClick={onClickCreateBtn}
          >
            添加成员
          </AuthButton>
        </div>
      </div>

      <Table
        dataSource={workspaceUserList}
        columns={columns}
        rowKey="entityId"
        scroll={{x: 1228}}
        loading={{
          loading: queryWorkspaceUserListLoading,
          size: 'small'
        }}
        pagination={false}
        onChange={onTableChange}
        className={styles['table-container']}
      />

      {pagination.total > 0 && (
        <div className={styles['pagination-container']}>
          <Pagination
            // showSizeChanger={true}
            // showQuickJumper={true}
            // showTotal={showTotal}
            current={pagination.pageNo}
            total={pagination.total}
            onChange={(page, pageSize = 10) => {
              setPagination((prev) => ({
                ...prev,
                pageNo: page,
                pageSize: pageSize
              }));
              getWorkspaceUserList();
            }}
          />
        </div>
      )}
      <CreateModal
        workspaceId={workspaceDetail?.id || ''}
        isModalVisible={isModalVisible}
        currentUser={currentUser}
        handleCloseModal={handleCloseModal}
        getWorkspaceUserList={getWorkspaceUserList}
        getWorkspaceDetail={getWorkspaceDetail}
      />
    </div>
  );
};

export default WorkspacePermissionTab;
