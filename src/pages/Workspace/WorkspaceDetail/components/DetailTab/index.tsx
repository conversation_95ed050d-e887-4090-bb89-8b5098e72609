/**
 * @file 工作空间详情 - 详情tab页
 * <AUTHOR>
 */

import {FC, useCallback, useMemo, useState} from 'react';
import {Button, Popconfirm, Input, toast, Form} from 'acud';
import {Clipboard} from '@baidu/bce-react-toolkit';
import {useRequest} from 'ahooks';
import {updateWorkspace, IWorkspace, checkWorkspaceNameDuplicate} from '@api/workspace';
import {WorkspaceStatusMap} from '../../../constants';
import {formatTime} from '@utils/moment';
import IconSvg from '@components/IconSvg';
import styles from './index.module.less';
import {RULE} from '@utils/regs';
import {Privilege} from '@api/permission/type';
import AuthButton from '@components/AuthComponents/AuthButton';

const PREFIX = 'workspace-detail-tab';

interface IWorkspaceDetailTabProps {
  workspaceDetail?: IWorkspace;
  getWorkspaceDetail: () => void;
}

const defaultEditVisible = {
  nameEdit: false,
  descEdit: false
};

const WorkspaceDetailTab: FC<IWorkspaceDetailTabProps> = ({workspaceDetail, getWorkspaceDetail}) => {
  // 名称和描述弹窗显隐
  const [editVisible, setEditVisible] = useState(defaultEditVisible);
  // 描述值
  const [descInput, setDescInput] = useState<string>('');
  // 名称值
  const [form] = Form.useForm();

  const isManage = useMemo(
    () => workspaceDetail?.privileges?.includes(Privilege.FullControl),
    [workspaceDetail?.privileges]
  );

  const {run: runUpdateWorkspace, loading: updateWorkspaceLoading} = useRequest(updateWorkspace, {
    manual: true,
    onSuccess: (res) => {
      if (res.success) {
        getWorkspaceDetail();
        setEditVisible(defaultEditVisible);
        toast.success({message: '操作成功', duration: 5});
      }
    }
  });

  // 更新名称或描述，交互上实现分别更新
  const handleUpdateInfo = useCallback(
    async (type: 'name' | 'desc') => {
      const payload: Record<string, any> = {};

      if (type === 'name') {
        try {
          await form.validateFields();
          payload.name = form.getFieldValue('name');
        } catch {
          return;
        }
      }

      if (type === 'desc') {
        payload.desc = descInput;
      }

      runUpdateWorkspace(workspaceDetail?.id || '', payload);
    },
    [workspaceDetail, descInput, form, runUpdateWorkspace]
  );

  // 渲染详情项
  const getRender = () => {
    const {
      name,
      status,
      storageLocation = '',
      metastore,
      desc,
      creator,
      createdAt,
      id
    } = workspaceDetail || {};
    return [
      {
        label: '空间名称',
        key: 'name',
        render: (
          <span>
            {name || '-'}
            <Popconfirm
              icon={false}
              visible={editVisible.nameEdit}
              getPopupContainer={(node) => node.parentElement!}
              overlayClassName={styles[`${PREFIX}-item-content-popconfirm`]}
              okButtonProps={{loading: updateWorkspaceLoading}}
              title={
                <div>
                  <Form form={form} className={styles[`${PREFIX}-form-name`]}>
                    <Form.Item
                      name="name"
                      // extra={`${RULE.specialName50Text}，全局唯⼀`}
                      validateDebounce={1000}
                      rules={[
                        {required: true, message: '请输入空间中文名称'},
                        {
                          validator: async (_, value) => {
                            // 校验特殊字符和长度限制
                            if (!RULE.specialName50.test(value)) {
                              return Promise.reject(new Error(`${RULE.specialName50Text}，全局唯⼀`));
                            }
                            if (value !== name) {
                              // 校验空间名称是否重复
                              const res = await checkWorkspaceNameDuplicate({name: value});
                              if (res.success && res.result) {
                                return Promise.reject(new Error('该空间已存在，请重新输入'));
                              }
                            }
                            return Promise.resolve();
                          }
                        }
                      ]}
                    >
                      <Input placeholder="请输入空间中文名称" allowClear limitLength={50} />
                    </Form.Item>
                  </Form>
                </div>
              }
              onConfirm={() => handleUpdateInfo('name')}
              onCancel={() => setEditVisible(defaultEditVisible)}
              onVisibleChange={(visible) => {
                visible && form.setFieldsValue({name: name || ''});
              }}
            >
              <AuthButton
                isAuth={isManage}
                icon={<IconSvg type="edit" size={16} />}
                size="small"
                type="actiontext"
                onClick={(e) => {
                  setEditVisible({descEdit: false, nameEdit: true});
                }}
                className={styles[`${PREFIX}-item-desc-edit`]}
              ></AuthButton>
            </Popconfirm>
          </span>
        )
      },
      {
        label: '空间ID',
        key: 'id',
        render: (
          <>
            <span>
              {id || '-'}
              {!!id && (
                <Clipboard text={id} successMessage="复制成功" className={styles[`${PREFIX}-item-copy`]}>
                  <Button icon={<IconSvg type="copy" size={16} />} size="small" type="actiontext"></Button>
                </Clipboard>
              )}
            </span>
          </>
        )
      },
      {
        label: '存储路径',
        key: 'storageLocation',
        render: (
          <>
            <span>
              {storageLocation || '-'}
              {!!storageLocation && (
                <Clipboard
                  text={storageLocation}
                  successMessage="复制成功"
                  className={styles[`${PREFIX}-item-copy`]}
                >
                  <Button icon={<IconSvg type="copy" size={16} />} size="small" type="actiontext"></Button>
                </Clipboard>
              )}
            </span>
          </>
        )
      },
      {
        label: '空间状态',
        key: 'status',
        render: <span>{status ? WorkspaceStatusMap[status] : '-'}</span>
      },
      {
        label: '元存储',
        key: 'metastore',
        render: <span>{metastore || '-'}</span>
      },
      {
        label: '创建人',
        key: 'creator',
        render: <span>{creator || '-'}</span>
      },
      {
        label: '创建时间',
        key: 'createdAt',
        render: <span>{createdAt ? formatTime(createdAt) : '-'}</span>
      },
      {
        label: '描述',
        key: 'desc',
        render: (
          <span>
            {desc || '-'}
            <Popconfirm
              icon={false}
              visible={editVisible.descEdit}
              getPopupContainer={(node) => node.parentElement!}
              overlayClassName={styles[`${PREFIX}-item-content-popconfirm`]}
              okButtonProps={{loading: updateWorkspaceLoading}}
              title={
                <div>
                  <Input.TextArea
                    value={descInput}
                    limitLength={200}
                    autoSize={{minRows: 4, maxRows: 8}}
                    onChange={(e) => {
                      setDescInput(e.target.value);
                    }}
                    placeholder="请输入描述"
                  />
                </div>
              }
              onConfirm={() => handleUpdateInfo('desc')}
              onCancel={() => setEditVisible(defaultEditVisible)}
              onVisibleChange={(visible) => {
                visible && setDescInput(desc || '');
              }}
            >
              <AuthButton
                isAuth={isManage}
                icon={<IconSvg type="edit" size={16} />}
                size="small"
                type="actiontext"
                onClick={(e) => {
                  setEditVisible({descEdit: true, nameEdit: false});
                }}
                className={styles[`${PREFIX}-item-desc-edit`]}
              ></AuthButton>
            </Popconfirm>
          </span>
        )
      }
    ];
  };

  return (
    <div className={styles[PREFIX]}>
      {getRender().map((item) => (
        <div className={styles[`${PREFIX}-item`]} key={item.key}>
          <div className={styles[`${PREFIX}-item-label`]}>{item.label}</div>
          <div className={styles[`${PREFIX}-item-content`]}>{item.render}</div>
        </div>
      ))}
    </div>
  );
};

export default WorkspaceDetailTab;
